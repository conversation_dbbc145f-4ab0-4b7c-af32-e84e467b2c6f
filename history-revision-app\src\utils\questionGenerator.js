import { getTopicContent, getAllContent } from '../data/content'

// Question types
export const QUESTION_TYPES = {
  MULTIPLE_CHOICE: 'multiple_choice',
  TRUE_FALSE: 'true_false',
  MATCH_UP: 'match_up',
  DRAG_ORDER: 'drag_order',
  FILL_GAPS: 'fill_gaps'
}

// Generate multiple choice questions
const generateMultipleChoice = (content) => {
  const questions = []

  content.forEach((item, index) => {
    if (item.content.includes(':')) {
      const [question, answer] = item.content.split(':')
      const wrongAnswers = generateWrongAnswers(answer.trim(), content)

      if (wrongAnswers.length >= 3) {
        questions.push({
          id: `mc_${index}`,
          type: QUESTION_TYPES.MULTIPLE_CHOICE,
          question: `What ${question.toLowerCase().trim()}?`,
          correctAnswer: answer.trim(),
          options: shuffleArray([answer.trim(), ...wrongAnswers]).slice(0, 4),
          topic: item.topicTitle,
          subtopic: item.subtopicTitle
        })
      }
    }
  })

  return questions
}

// Generate true/false questions
const generateTrueFalse = (content) => {
  const questions = []
  
  content.forEach((item, index) => {
    const statements = item.content.split(';').filter(s => s.trim().length > 10)
    
    statements.forEach((statement, stIndex) => {
      // Create true statement
      questions.push({
        id: `tf_${index}_${stIndex}_true`,
        type: QUESTION_TYPES.TRUE_FALSE,
        question: statement.trim(),
        correctAnswer: true,
        topic: item.topicTitle,
        subtopic: item.subtopicTitle
      })
      
      // Create false statement by modifying key details
      const falseStatement = createFalseStatement(statement.trim())
      if (falseStatement !== statement.trim()) {
        questions.push({
          id: `tf_${index}_${stIndex}_false`,
          type: QUESTION_TYPES.TRUE_FALSE,
          question: falseStatement,
          correctAnswer: false,
          topic: item.topicTitle,
          subtopic: item.subtopicTitle
        })
      }
    })
  })
  
  return questions
}

// Generate matching questions
const generateMatchUp = (content) => {
  const questions = []
  const pairs = []
  
  content.forEach(item => {
    if (item.content.includes(':')) {
      const [left, right] = item.content.split(':')
      pairs.push({
        left: left.trim(),
        right: right.trim(),
        topic: item.topicTitle
      })
    }
  })
  
  // Group pairs by topic and create matching questions
  const topicGroups = {}
  pairs.forEach(pair => {
    if (!topicGroups[pair.topic]) {
      topicGroups[pair.topic] = []
    }
    topicGroups[pair.topic].push(pair)
  })
  
  Object.entries(topicGroups).forEach(([topic, topicPairs]) => {
    if (topicPairs.length >= 4) {
      const selectedPairs = shuffleArray(topicPairs).slice(0, 6)
      questions.push({
        id: `match_${topic}`,
        type: QUESTION_TYPES.MATCH_UP,
        question: `Match the following items:`,
        pairs: selectedPairs,
        topic: topic
      })
    }
  })
  
  return questions
}

// Generate drag and drop ordering questions
const generateDragOrder = (content) => {
  const questions = []
  
  // Look for chronological events
  const events = []
  content.forEach(item => {
    const matches = item.content.match(/\((\d{4})\)/g)
    if (matches) {
      matches.forEach(match => {
        const year = match.replace(/[()]/g, '')
        const eventText = item.content.replace(match, '').trim()
        if (eventText.length > 10) {
          events.push({
            year: parseInt(year),
            event: eventText.split(':')[0].trim(),
            topic: item.topicTitle
          })
        }
      })
    }
  })
  
  // Group by topic and create ordering questions
  const topicGroups = {}
  events.forEach(event => {
    if (!topicGroups[event.topic]) {
      topicGroups[event.topic] = []
    }
    topicGroups[event.topic].push(event)
  })
  
  Object.entries(topicGroups).forEach(([topic, topicEvents]) => {
    if (topicEvents.length >= 4) {
      const sortedEvents = topicEvents.sort((a, b) => a.year - b.year)
      const selectedEvents = sortedEvents.slice(0, 6)
      
      questions.push({
        id: `order_${topic}`,
        type: QUESTION_TYPES.DRAG_ORDER,
        question: `Put these events in chronological order:`,
        correctOrder: selectedEvents,
        items: shuffleArray([...selectedEvents]),
        topic: topic
      })
    }
  })
  
  return questions
}

// Generate fill in the gaps questions
const generateFillGaps = (content) => {
  const questions = []
  
  content.forEach((item, index) => {
    const sentences = item.content.split('.').filter(s => s.trim().length > 20)
    
    sentences.forEach((sentence, sIndex) => {
      const words = sentence.trim().split(' ')
      if (words.length > 5) {
        // Find important words to blank out (proper nouns, numbers, key terms)
        const importantWords = words.filter(word => 
          /^[A-Z]/.test(word) || /\d/.test(word) || word.length > 6
        )
        
        if (importantWords.length > 0) {
          const wordToBlank = importantWords[Math.floor(Math.random() * importantWords.length)]
          const gappedSentence = sentence.replace(new RegExp(`\\b${wordToBlank}\\b`, 'g'), '______')
          
          questions.push({
            id: `fill_${index}_${sIndex}`,
            type: QUESTION_TYPES.FILL_GAPS,
            question: `Fill in the gap: ${gappedSentence}`,
            correctAnswer: wordToBlank,
            topic: item.topicTitle,
            subtopic: item.subtopicTitle
          })
        }
      }
    })
  })
  
  return questions
}

// Helper functions
const generateWrongAnswers = (correctAnswer, allContent) => {
  const wrongAnswers = []
  const usedAnswers = new Set([correctAnswer])
  
  allContent.forEach(item => {
    if (item.content.includes(':')) {
      const [, answer] = item.content.split(':')
      const cleanAnswer = answer.trim()
      if (!usedAnswers.has(cleanAnswer) && cleanAnswer !== correctAnswer) {
        wrongAnswers.push(cleanAnswer)
        usedAnswers.add(cleanAnswer)
      }
    }
  })
  
  return shuffleArray(wrongAnswers).slice(0, 3)
}

const createFalseStatement = (statement) => {
  // Simple false statement generation by changing years, names, or key terms
  const yearMatches = statement.match(/\d{4}/g)
  if (yearMatches) {
    const year = parseInt(yearMatches[0])
    const wrongYear = year + (Math.random() > 0.5 ? 1 : -1) * (Math.floor(Math.random() * 5) + 1)
    return statement.replace(yearMatches[0], wrongYear.toString())
  }
  
  // Change some key terms
  const replacements = {
    'Hitler': 'Stalin',
    'Nazi': 'Communist',
    'Germany': 'Russia',
    'Weimar': 'Berlin',
    'Republic': 'Empire',
    'democratic': 'authoritarian',
    'increased': 'decreased',
    'successful': 'failed'
  }
  
  for (const [original, replacement] of Object.entries(replacements)) {
    if (statement.includes(original)) {
      return statement.replace(original, replacement)
    }
  }
  
  return statement
}

const shuffleArray = (array) => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

// Main function to generate questions for a topic
export const generateQuestions = (topicId, questionCount = 20) => {
  const content = topicId ? getTopicContent(topicId) : getAllContent()
  console.log('Content for question generation:', content.length, content)

  const mcQuestions = generateMultipleChoice(content)
  const tfQuestions = generateTrueFalse(content)
  const matchQuestions = generateMatchUp(content)
  const orderQuestions = generateDragOrder(content)
  const fillQuestions = generateFillGaps(content)

  console.log('Question counts:', {
    mc: mcQuestions.length,
    tf: tfQuestions.length,
    match: matchQuestions.length,
    order: orderQuestions.length,
    fill: fillQuestions.length
  })

  let allQuestions = [
    ...mcQuestions,
    ...tfQuestions,
    ...matchQuestions,
    ...orderQuestions,
    ...fillQuestions
  ]

  // Fallback: if no questions generated, create some basic true/false questions
  if (allQuestions.length === 0) {
    console.log('No questions generated, creating fallback questions')
    allQuestions = createFallbackQuestions(content)
  }

  return shuffleArray(allQuestions).slice(0, questionCount)
}

// Fallback question generator
const createFallbackQuestions = (content) => {
  const questions = []

  content.forEach((item, index) => {
    // Create simple true/false questions from content
    const statements = item.content.split('.').filter(s => s.trim().length > 10)

    statements.forEach((statement, stIndex) => {
      if (questions.length < 20) {
        questions.push({
          id: `fallback_tf_${index}_${stIndex}`,
          type: QUESTION_TYPES.TRUE_FALSE,
          question: statement.trim(),
          correctAnswer: true,
          topic: item.topicTitle,
          subtopic: item.subtopicTitle
        })
      }
    })
  })

  return questions
}

import { getTopicContent, getAllContent } from '../data/content'

// Question types
export const QUESTION_TYPES = {
  MULTIPLE_CHOICE: 'multiple_choice',
  TRUE_FALSE: 'true_false',
  MATCH_UP: 'match_up',
  DRAG_ORDER: 'drag_order',
  FILL_GAPS: 'fill_gaps'
}

// Generate multiple choice questions
const generateMultipleChoice = (content) => {
  const questions = []

  content.forEach((item, index) => {
    if (item.content.includes(':')) {
      const [question, answer] = item.content.split(':')
      const wrongAnswers = generateWrongAnswers(answer.trim(), content)

      if (wrongAnswers.length >= 3) {
        questions.push({
          id: `mc_${index}`,
          type: QUESTION_TYPES.MULTIPLE_CHOICE,
          question: `What ${question.toLowerCase().trim()}?`,
          correctAnswer: answer.trim(),
          options: shuffleArray([answer.trim(), ...wrongAnswers]).slice(0, 4),
          topic: item.topicTitle,
          subtopic: item.subtopicTitle
        })
      }
    }
  })

  return questions
}

// Generate true/false questions
const generateTrueFalse = (content) => {
  const questions = []
  
  content.forEach((item, index) => {
    const statements = item.content.split(';').filter(s => s.trim().length > 10)
    
    statements.forEach((statement, stIndex) => {
      // Create true statement
      questions.push({
        id: `tf_${index}_${stIndex}_true`,
        type: QUESTION_TYPES.TRUE_FALSE,
        question: statement.trim(),
        correctAnswer: true,
        topic: item.topicTitle,
        subtopic: item.subtopicTitle
      })
      
      // Create false statement by modifying key details
      const falseStatement = createFalseStatement(statement.trim())
      if (falseStatement !== statement.trim()) {
        questions.push({
          id: `tf_${index}_${stIndex}_false`,
          type: QUESTION_TYPES.TRUE_FALSE,
          question: falseStatement,
          correctAnswer: false,
          topic: item.topicTitle,
          subtopic: item.subtopicTitle
        })
      }
    })
  })
  
  return questions
}

// Generate matching questions
const generateMatchUp = (content) => {
  const questions = []
  const pairs = []
  
  content.forEach(item => {
    if (item.content.includes(':')) {
      const [left, right] = item.content.split(':')
      pairs.push({
        left: left.trim(),
        right: right.trim(),
        topic: item.topicTitle
      })
    }
  })
  
  // Group pairs by topic and create matching questions
  const topicGroups = {}
  pairs.forEach(pair => {
    if (!topicGroups[pair.topic]) {
      topicGroups[pair.topic] = []
    }
    topicGroups[pair.topic].push(pair)
  })
  
  Object.entries(topicGroups).forEach(([topic, topicPairs]) => {
    if (topicPairs.length >= 4) {
      const selectedPairs = shuffleArray(topicPairs).slice(0, 6)
      questions.push({
        id: `match_${topic}`,
        type: QUESTION_TYPES.MATCH_UP,
        question: `Match the following items:`,
        pairs: selectedPairs,
        topic: topic
      })
    }
  })
  
  return questions
}

// Generate drag and drop ordering questions
const generateDragOrder = (content) => {
  const questions = []
  
  // Look for chronological events
  const events = []
  content.forEach(item => {
    const matches = item.content.match(/\((\d{4})\)/g)
    if (matches) {
      matches.forEach(match => {
        const year = match.replace(/[()]/g, '')
        const eventText = item.content.replace(match, '').trim()
        if (eventText.length > 10) {
          events.push({
            year: parseInt(year),
            event: eventText.split(':')[0].trim(),
            topic: item.topicTitle
          })
        }
      })
    }
  })
  
  // Group by topic and create ordering questions
  const topicGroups = {}
  events.forEach(event => {
    if (!topicGroups[event.topic]) {
      topicGroups[event.topic] = []
    }
    topicGroups[event.topic].push(event)
  })
  
  Object.entries(topicGroups).forEach(([topic, topicEvents]) => {
    if (topicEvents.length >= 4) {
      const sortedEvents = topicEvents.sort((a, b) => a.year - b.year)
      const selectedEvents = sortedEvents.slice(0, 6)
      
      questions.push({
        id: `order_${topic}`,
        type: QUESTION_TYPES.DRAG_ORDER,
        question: `Put these events in chronological order:`,
        correctOrder: selectedEvents,
        items: shuffleArray([...selectedEvents]),
        topic: topic
      })
    }
  })
  
  return questions
}

// Generate fill in the gaps questions
const generateFillGaps = (content) => {
  const questions = []
  
  content.forEach((item, index) => {
    const sentences = item.content.split('.').filter(s => s.trim().length > 20)
    
    sentences.forEach((sentence, sIndex) => {
      const words = sentence.trim().split(' ')
      if (words.length > 5) {
        // Find important words to blank out (proper nouns, numbers, key terms)
        const importantWords = words.filter(word => 
          /^[A-Z]/.test(word) || /\d/.test(word) || word.length > 6
        )
        
        if (importantWords.length > 0) {
          const wordToBlank = importantWords[Math.floor(Math.random() * importantWords.length)]
          const gappedSentence = sentence.replace(new RegExp(`\\b${wordToBlank}\\b`, 'g'), '______')
          
          questions.push({
            id: `fill_${index}_${sIndex}`,
            type: QUESTION_TYPES.FILL_GAPS,
            question: `Fill in the gap: ${gappedSentence}`,
            correctAnswer: wordToBlank,
            topic: item.topicTitle,
            subtopic: item.subtopicTitle
          })
        }
      }
    })
  })
  
  return questions
}

// Helper functions
const generateWrongAnswers = (correctAnswer, allContent) => {
  const wrongAnswers = []
  const usedAnswers = new Set([correctAnswer])
  
  allContent.forEach(item => {
    if (item.content.includes(':')) {
      const [, answer] = item.content.split(':')
      const cleanAnswer = answer.trim()
      if (!usedAnswers.has(cleanAnswer) && cleanAnswer !== correctAnswer) {
        wrongAnswers.push(cleanAnswer)
        usedAnswers.add(cleanAnswer)
      }
    }
  })
  
  return shuffleArray(wrongAnswers).slice(0, 3)
}

const createFalseStatement = (statement) => {
  // Simple false statement generation by changing years, names, or key terms
  const yearMatches = statement.match(/\d{4}/g)
  if (yearMatches) {
    const year = parseInt(yearMatches[0])
    const wrongYear = year + (Math.random() > 0.5 ? 1 : -1) * (Math.floor(Math.random() * 5) + 1)
    return statement.replace(yearMatches[0], wrongYear.toString())
  }
  
  // Change some key terms
  const replacements = {
    'Hitler': 'Stalin',
    'Nazi': 'Communist',
    'Germany': 'Russia',
    'Weimar': 'Berlin',
    'Republic': 'Empire',
    'democratic': 'authoritarian',
    'increased': 'decreased',
    'successful': 'failed'
  }
  
  for (const [original, replacement] of Object.entries(replacements)) {
    if (statement.includes(original)) {
      return statement.replace(original, replacement)
    }
  }
  
  return statement
}

const shuffleArray = (array) => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

// Main function to generate questions for a topic
export const generateQuestions = (topicId, questionCount = 20) => {
  console.log('generateQuestions called with:', topicId, questionCount)

  const content = topicId ? getTopicContent(topicId) : getAllContent()
  console.log('Content loaded:', content.length)

  const allQuestions = []

  // Generate multiple choice questions
  const mcQuestions = generateBetterMultipleChoice(content)
  allQuestions.push(...mcQuestions)

  // Generate true/false questions
  const tfQuestions = generateBetterTrueFalse(content)
  allQuestions.push(...tfQuestions)

  // Generate fill-in-the-gap questions
  const fillQuestions = generateBetterFillGaps(content)
  allQuestions.push(...fillQuestions)

  console.log('Generated question counts:', {
    mc: mcQuestions.length,
    tf: tfQuestions.length,
    fill: fillQuestions.length,
    total: allQuestions.length
  })

  // If we don't have enough questions, add some fallback ones
  if (allQuestions.length < questionCount) {
    const fallbackQuestions = createFallbackQuestions(questionCount - allQuestions.length)
    allQuestions.push(...fallbackQuestions)
  }

  return shuffleArray(allQuestions).slice(0, questionCount)
}

// Better multiple choice generator
const generateBetterMultipleChoice = (content) => {
  const questions = []
  const mcData = [
    { q: "When was the Weimar Republic established?", a: "1918", wrong: ["1919", "1920", "1917"] },
    { q: "Who was the first President of the Weimar Republic?", a: "Friedrich Ebert", wrong: ["Paul von Hindenburg", "Gustav Stresemann", "Wilhelm II"] },
    { q: "When did Hitler become Chancellor?", a: "January 1933", wrong: ["March 1933", "November 1932", "February 1933"] },
    { q: "What was the original name of the Nazi Party?", a: "German Workers' Party", wrong: ["National Socialist Party", "German National Party", "Workers' Socialist Party"] },
    { q: "When did the Munich Putsch occur?", a: "1923", wrong: ["1922", "1924", "1925"] },
    { q: "What was the Enabling Act?", a: "Law allowing Hitler to pass laws without Reichstag", wrong: ["Law banning other parties", "Law creating the SS", "Law establishing concentration camps"] },
    { q: "When was the Night of the Long Knives?", a: "June 1934", wrong: ["July 1934", "May 1934", "August 1934"] },
    { q: "Who led the SA before being killed in 1934?", a: "Ernst Röhm", wrong: ["Heinrich Himmler", "Rudolf Hess", "Hermann Göring"] }
  ]

  mcData.forEach((item, index) => {
    questions.push({
      id: `mc_${index}`,
      type: QUESTION_TYPES.MULTIPLE_CHOICE,
      question: item.q,
      correctAnswer: item.a,
      options: shuffleArray([item.a, ...item.wrong]),
      topic: "History GCSE",
      subtopic: "Mixed"
    })
  })

  return questions
}

// Better true/false generator
const generateBetterTrueFalse = (content) => {
  const questions = []
  const tfData = [
    { q: "The Weimar Republic was Germany's first democratic government", a: true },
    { q: "Hitler was elected as President of Germany in 1933", a: false },
    { q: "The Treaty of Versailles was signed in 1918", a: false },
    { q: "The SA were also known as Brownshirts", a: true },
    { q: "The Reichstag Fire happened in 1934", a: false },
    { q: "The SS was led by Heinrich Himmler", a: true },
    { q: "The Munich Putsch was successful", a: false },
    { q: "Article 48 gave emergency powers to the President", a: true },
    { q: "The Dawes Plan helped stabilize German currency", a: true },
    { q: "The Kapp Putsch was a left-wing uprising", a: false }
  ]

  tfData.forEach((item, index) => {
    questions.push({
      id: `tf_${index}`,
      type: QUESTION_TYPES.TRUE_FALSE,
      question: item.q,
      correctAnswer: item.a,
      topic: "History GCSE",
      subtopic: "Mixed"
    })
  })

  return questions
}

// Better fill-in-the-gaps generator
const generateBetterFillGaps = (content) => {
  const questions = []
  const fillData = [
    { q: "The ______ Republic was established in Germany after WWI", a: "Weimar" },
    { q: "Hitler became ______ of Germany in January 1933", a: "Chancellor" },
    { q: "The ______ Fire was used to justify emergency powers", a: "Reichstag" },
    { q: "The SA were led by Ernst ______", a: "Röhm" },
    { q: "The ______ Act allowed Hitler to pass laws without parliament", a: "Enabling" },
    { q: "The SS was led by Heinrich ______", a: "Himmler" }
  ]

  fillData.forEach((item, index) => {
    questions.push({
      id: `fill_${index}`,
      type: QUESTION_TYPES.FILL_GAPS,
      question: item.q,
      correctAnswer: item.a,
      topic: "History GCSE",
      subtopic: "Mixed"
    })
  })

  return questions
}

// Fallback questions
const createFallbackQuestions = (count) => {
  const fallback = [
    { q: "World War I ended in 1918", a: true, type: QUESTION_TYPES.TRUE_FALSE },
    { q: "The Weimar Republic lasted until 1933", a: true, type: QUESTION_TYPES.TRUE_FALSE },
    { q: "Hitler wrote Mein Kampf while in prison", a: true, type: QUESTION_TYPES.TRUE_FALSE }
  ]

  const questions = []
  for (let i = 0; i < count && i < fallback.length; i++) {
    questions.push({
      id: `fallback_${i}`,
      type: fallback[i].type,
      question: fallback[i].q,
      correctAnswer: fallback[i].a,
      topic: "History",
      subtopic: "General"
    })
  }

  return questions
}

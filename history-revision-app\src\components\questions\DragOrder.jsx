import { useState } from 'react'
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd'
import { Check, X, GripVertical, Calendar } from 'lucide-react'

const DragOrder = ({ question, onAnswer, isAnswered, userAnswer }) => {
  const [items, setItems] = useState(userAnswer || question.items)
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered)

  const handleDragEnd = (result) => {
    if (!result.destination || isAnswered) return

    const newItems = Array.from(items)
    const [reorderedItem] = newItems.splice(result.source.index, 1)
    newItems.splice(result.destination.index, 0, reorderedItem)

    setItems(newItems)
  }

  const handleSubmit = () => {
    if (hasSubmitted) return

    // Check if order is correct
    const isCorrect = items.every((item, index) => 
      item.year === question.correctOrder[index].year
    )

    setHasSubmitted(true)
    onAnswer(items, isCorrect)
  }

  const getItemStyle = (item, index) => {
    if (!hasSubmitted) {
      return 'border-slate-700 hover:border-blue-400'
    }

    const correctItem = question.correctOrder[index]
    return item.year === correctItem.year
      ? 'border-green-500 bg-green-500/10'
      : 'border-red-500 bg-red-500/10'
  }

  const getItemIcon = (item, index) => {
    if (!hasSubmitted) return null

    const correctItem = question.correctOrder[index]
    return item.year === correctItem.year
      ? <Check className="w-4 h-4 text-green-500" />
      : <X className="w-4 h-4 text-red-500" />
  }

  const correctCount = hasSubmitted 
    ? items.filter((item, index) => item.year === question.correctOrder[index].year).length
    : 0

  return (
    <div className="space-y-6">
      <div>
        <div className="text-sm text-orange-500 font-medium mb-2">Chronological Order</div>
        <h3 className="text-xl font-semibold text-slate-100 mb-2">{question.question}</h3>
        {question.topic && (
          <div className="text-sm text-slate-400">Topic: {question.topic}</div>
        )}
        <p className="text-slate-400 text-sm mt-2">
          Drag and drop the events to arrange them in chronological order (earliest to latest)
        </p>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="timeline">
          {(provided, snapshot) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className={`space-y-3 p-4 rounded-lg border-2 border-dashed transition-colors ${
                snapshot.isDraggingOver ? 'border-blue-500 bg-blue-500/5' : 'border-slate-700'
              }`}
            >
              {items.map((item, index) => (
                <Draggable
                  key={`${item.event}-${item.year}`}
                  draggableId={`${item.event}-${item.year}`}
                  index={index}
                  isDragDisabled={isAnswered}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                        snapshot.isDragging ? 'shadow-lg rotate-2' : ''
                      } ${getItemStyle(item, index)} ${
                        isAnswered ? 'cursor-default' : 'cursor-move'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          {...provided.dragHandleProps}
                          className={`p-1 rounded ${isAnswered ? 'text-slate-400' : 'text-slate-400 hover:text-slate-100'}`}
                        >
                          <GripVertical className="w-5 h-5" />
                        </div>
                        
                        <div className="flex items-center gap-2 text-slate-400">
                          <Calendar className="w-4 h-4" />
                          <span className="text-sm font-mono">{item.year}</span>
                        </div>
                        
                        <div className="flex-1">
                          <span className="text-slate-100">{item.event}</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-slate-400">#{index + 1}</div>
                          {getItemIcon(item, index)}
                        </div>
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {!hasSubmitted && (
        <div className="text-center">
          <button
            onClick={handleSubmit}
            className="btn-primary"
          >
            Submit Order
          </button>
        </div>
      )}

      {hasSubmitted && (
        <div className={`p-4 rounded-lg border ${
          correctCount === question.correctOrder.length
            ? 'border-green-500 bg-green-500/10'
            : 'border-red-500 bg-red-500/10'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {correctCount === question.correctOrder.length ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <X className="w-5 h-5 text-red-500" />
            )}
            <span className={`font-medium ${
              correctCount === question.correctOrder.length ? 'text-green-500' : 'text-red-500'
            }`}>
              {correctCount === question.correctOrder.length ? 'Perfect chronological order!' : 'Some events are out of order'}
            </span>
          </div>
          <p className="text-slate-400 text-sm mb-3">
            You got {correctCount} out of {question.correctOrder.length} events in the correct position
          </p>
          
          <div className="space-y-2">
            <h4 className="font-medium text-slate-100 text-sm">Correct chronological order:</h4>
            {question.correctOrder.map((item, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <span className="text-slate-400">#{index + 1}</span>
                <span className="font-mono text-green-500">{item.year}</span>
                <span className="text-slate-100">{item.event}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default DragOrder

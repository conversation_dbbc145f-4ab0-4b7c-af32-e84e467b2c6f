@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-slate-900 text-slate-100 font-sans;
    margin: 0;
    min-height: 100vh;
  }

  html {
    /* Dark mode is handled by the class attribute in the HTML */
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-slate-800 hover:bg-slate-600 text-slate-100 border border-slate-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-slate-800 border border-slate-700 rounded-lg p-6 shadow-lg;
  }

  .input {
    @apply bg-slate-800 border border-slate-700 text-slate-100 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
}

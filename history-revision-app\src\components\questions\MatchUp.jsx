import { useState } from 'react'
import { Check, X, Link } from 'lucide-react'

const MatchUp = ({ question, onAnswer, isAnswered, userAnswer }) => {
  const [matches, setMatches] = useState(userAnswer || {})
  const [selectedLeft, setSelectedLeft] = useState(null)

  const leftItems = question.pairs.map(pair => pair.left)
  const rightItems = [...question.pairs.map(pair => pair.right)].sort(() => Math.random() - 0.5)

  const handleLeftClick = (leftItem) => {
    if (isAnswered) return
    setSelectedLeft(leftItem)
  }

  const handleRightClick = (rightItem) => {
    if (isAnswered || !selectedLeft) return

    const newMatches = { ...matches }
    
    // Remove any existing match for this right item
    Object.keys(newMatches).forEach(key => {
      if (newMatches[key] === rightItem) {
        delete newMatches[key]
      }
    })
    
    // Add new match
    newMatches[selectedLeft] = rightItem
    setMatches(newMatches)
    setSelectedLeft(null)

    // Check if all items are matched
    if (Object.keys(newMatches).length === question.pairs.length) {
      // Calculate score
      let correctMatches = 0
      question.pairs.forEach(pair => {
        if (newMatches[pair.left] === pair.right) {
          correctMatches++
        }
      })
      
      const isCorrect = correctMatches === question.pairs.length
      onAnswer(newMatches, isCorrect)
    }
  }

  const getLeftItemStyle = (leftItem) => {
    if (selectedLeft === leftItem) {
      return 'border-blue-500 bg-blue-500/10'
    }
    if (matches[leftItem]) {
      if (isAnswered) {
        const correctMatch = question.pairs.find(p => p.left === leftItem)?.right
        return matches[leftItem] === correctMatch
          ? 'border-green-500 bg-green-500/10'
          : 'border-red-500 bg-red-500/10'
      }
      return 'border-yellow-500 bg-yellow-500/10'
    }
    return 'border-slate-700 hover:border-blue-400'
  }

  const getRightItemStyle = (rightItem) => {
    const isMatched = Object.values(matches).includes(rightItem)
    
    if (isMatched && isAnswered) {
      const leftItem = Object.keys(matches).find(key => matches[key] === rightItem)
      const correctMatch = question.pairs.find(p => p.left === leftItem)?.right
      return rightItem === correctMatch
        ? 'border-green-500 bg-green-500/10'
        : 'border-red-500 bg-red-500/10'
    }
    
    if (isMatched) {
      return 'border-yellow-500 bg-yellow-500/10'
    }
    
    return 'border-slate-700 hover:border-blue-400'
  }

  const getItemIcon = (item, isLeft = true) => {
    if (!isAnswered) return null

    if (isLeft) {
      const correctMatch = question.pairs.find(p => p.left === item)?.right
      return matches[item] === correctMatch
        ? <Check className="w-4 h-4 text-green-500" />
        : <X className="w-4 h-4 text-red-500" />
    } else {
      const leftItem = Object.keys(matches).find(key => matches[key] === item)
      if (!leftItem) return null
      
      const correctMatch = question.pairs.find(p => p.left === leftItem)?.right
      return item === correctMatch
        ? <Check className="w-4 h-4 text-green-500" />
        : <X className="w-4 h-4 text-red-500" />
    }
  }

  const correctMatches = isAnswered 
    ? question.pairs.filter(pair => matches[pair.left] === pair.right).length
    : 0

  return (
    <div className="space-y-6">
      <div>
        <div className="text-sm text-green-500 font-medium mb-2">Match Up</div>
        <h3 className="text-xl font-semibold text-slate-100 mb-2">{question.question}</h3>
        {question.topic && (
          <div className="text-sm text-slate-400">Topic: {question.topic}</div>
        )}
        <p className="text-slate-400 text-sm mt-2">
          Click an item on the left, then click its match on the right
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-3">
          <h4 className="font-medium text-slate-100 text-center">Items</h4>
          {leftItems.map((leftItem, index) => (
            <button
              key={index}
              onClick={() => handleLeftClick(leftItem)}
              disabled={isAnswered}
              className={`w-full p-3 rounded-lg border-2 text-left transition-all duration-200 ${getLeftItemStyle(leftItem)} ${
                isAnswered ? 'cursor-default' : 'cursor-pointer'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="text-slate-100">{leftItem}</span>
                <div className="flex items-center gap-2">
                  {matches[leftItem] && (
                    <Link className="w-4 h-4 text-yellow-500" />
                  )}
                  {getItemIcon(leftItem, true)}
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Right Column */}
        <div className="space-y-3">
          <h4 className="font-medium text-slate-100 text-center">Matches</h4>
          {rightItems.map((rightItem, index) => (
            <button
              key={index}
              onClick={() => handleRightClick(rightItem)}
              disabled={isAnswered || !selectedLeft}
              className={`w-full p-3 rounded-lg border-2 text-left transition-all duration-200 ${getRightItemStyle(rightItem)} ${
                isAnswered || !selectedLeft ? 'cursor-default' : 'cursor-pointer'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="text-slate-100">{rightItem}</span>
                {getItemIcon(rightItem, false)}
              </div>
            </button>
          ))}
        </div>
      </div>

      {isAnswered && (
        <div className={`p-4 rounded-lg border ${
          correctMatches === question.pairs.length
            ? 'border-green-500 bg-green-500/10'
            : 'border-red-500 bg-red-500/10'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {correctMatches === question.pairs.length ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <X className="w-5 h-5 text-red-500" />
            )}
            <span className={`font-medium ${
              correctMatches === question.pairs.length ? 'text-green-500' : 'text-red-500'
            }`}>
              {correctMatches === question.pairs.length ? 'Perfect!' : 'Some matches incorrect'}
            </span>
          </div>
          <p className="text-slate-400 text-sm">
            You got {correctMatches} out of {question.pairs.length} matches correct
          </p>
        </div>
      )}
    </div>
  )
}

export default MatchUp

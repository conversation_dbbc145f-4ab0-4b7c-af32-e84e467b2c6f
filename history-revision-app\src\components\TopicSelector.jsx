import { useState } from 'react'
import { Ch<PERSON><PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Clock, Target } from 'lucide-react'
import { getTopics } from '../data/content'

const TopicSelector = ({ onStartQuiz }) => {
  const [selectedTopic, setSelectedTopic] = useState(null)
  const topics = getTopics()

  const handleTopicSelect = (topic) => {
    setSelectedTopic(topic)
  }

  const handleStartQuiz = () => {
    if (selectedTopic) {
      onStartQuiz(selectedTopic)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-slate-100 mb-2">Choose Your Topic</h2>
        <p className="text-slate-400">Select a topic to start your revision quiz</p>
      </div>

      <div className="grid gap-4">
        {topics.map((topic) => (
          <div
            key={topic.id}
            className={`card cursor-pointer transition-all duration-200 hover:border-blue-500 ${
              selectedTopic?.id === topic.id 
                ? 'border-blue-500 bg-blue-500/10' 
                : 'hover:bg-slate-800/80'
            }`}
            onClick={() => handleTopicSelect(topic)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`p-3 rounded-lg ${
                  selectedTopic?.id === topic.id
                    ? 'bg-blue-500'
                    : 'bg-slate-700'
                }`}>
                  <BookOpen className={`w-6 h-6 ${
                    selectedTopic?.id === topic.id
                      ? 'text-white'
                      : 'text-slate-400'
                  }`} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-100">{topic.title}</h3>
                  <p className="text-slate-400 text-sm">
                    {topic.subtopics.length} subtopics available
                  </p>
                </div>
              </div>
              <ChevronRight className={`w-5 h-5 transition-colors ${
                selectedTopic?.id === topic.id
                  ? 'text-blue-500'
                  : 'text-slate-400'
              }`} />
            </div>

            {selectedTopic?.id === topic.id && (
              <div className="mt-4 pt-4 border-t border-slate-700">
                <h4 className="text-sm font-medium text-slate-100 mb-3">Subtopics:</h4>
                <div className="grid gap-2">
                  {topic.subtopics.map((subtopic) => (
                    <div key={subtopic.id} className="flex items-center gap-2 text-sm text-slate-400">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                      <span>{subtopic.title}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {selectedTopic && (
        <div className="card bg-blue-500/10 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-slate-100 mb-2">Ready to Start?</h3>
              <div className="flex items-center gap-6 text-sm text-slate-400">
                <div className="flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  <span>20 Questions</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>~15 minutes</span>
                </div>
              </div>
            </div>
            <button
              onClick={handleStartQuiz}
              className="btn-primary flex items-center gap-2"
            >
              Start Quiz
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      <div className="card bg-yellow-500/10 border-yellow-500">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-yellow-500 rounded-lg">
            <Target className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-slate-100">Mixed Practice</h3>
            <p className="text-slate-400 text-sm">Practice with questions from all topics</p>
          </div>
          <button
            onClick={() => onStartQuiz(null)}
            className="btn-secondary ml-auto"
          >
            Start Mixed Quiz
          </button>
        </div>
      </div>
    </div>
  )
}

export default TopicSelector

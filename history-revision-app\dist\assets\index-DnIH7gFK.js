(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function u(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(s){if(s.ep)return;s.ep=!0;const f=u(s);fetch(s.href,f)}})();function eg(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var oo={exports:{}},er={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var np;function Hb(){if(np)return er;np=1;var l=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(o,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var h in s)h!=="key"&&(f[h]=s[h])}else f=s;return s=f.ref,{$$typeof:l,type:o,key:d,ref:s!==void 0?s:null,props:f}}return er.Fragment=r,er.jsx=u,er.jsxs=u,er}var lp;function Lb(){return lp||(lp=1,oo.exports=Hb()),oo.exports}var S=Lb(),so={exports:{}},ue={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ap;function Yb(){if(ap)return ue;ap=1;var l=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.iterator;function x(A){return A===null||typeof A!="object"?null:(A=v&&A[v]||A["@@iterator"],typeof A=="function"?A:null)}var D={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,z={};function O(A,L,$){this.props=A,this.context=L,this.refs=z,this.updater=$||D}O.prototype.isReactComponent={},O.prototype.setState=function(A,L){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,L,"setState")},O.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function Y(){}Y.prototype=O.prototype;function Q(A,L,$){this.props=A,this.context=L,this.refs=z,this.updater=$||D}var K=Q.prototype=new Y;K.constructor=Q,M(K,O.prototype),K.isPureReactComponent=!0;var q=Array.isArray,H={H:null,A:null,T:null,S:null,V:null},J=Object.prototype.hasOwnProperty;function re(A,L,$,Z,ee,de){return $=de.ref,{$$typeof:l,type:A,key:L,ref:$!==void 0?$:null,props:de}}function ie(A,L){return re(A.type,L,void 0,void 0,void 0,A.props)}function we(A){return typeof A=="object"&&A!==null&&A.$$typeof===l}function ge(A){var L={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function($){return L[$]})}var ve=/\/+/g;function ye(A,L){return typeof A=="object"&&A!==null&&A.key!=null?ge(""+A.key):L.toString(36)}function $e(){}function Lt(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then($e,$e):(A.status="pending",A.then(function(L){A.status==="pending"&&(A.status="fulfilled",A.value=L)},function(L){A.status==="pending"&&(A.status="rejected",A.reason=L)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function Oe(A,L,$,Z,ee){var de=typeof A;(de==="undefined"||de==="boolean")&&(A=null);var le=!1;if(A===null)le=!0;else switch(de){case"bigint":case"string":case"number":le=!0;break;case"object":switch(A.$$typeof){case l:case r:le=!0;break;case y:return le=A._init,Oe(le(A._payload),L,$,Z,ee)}}if(le)return ee=ee(A),le=Z===""?"."+ye(A,0):Z,q(ee)?($="",le!=null&&($=le.replace(ve,"$&/")+"/"),Oe(ee,L,$,"",function(pt){return pt})):ee!=null&&(we(ee)&&(ee=ie(ee,$+(ee.key==null||A&&A.key===ee.key?"":(""+ee.key).replace(ve,"$&/")+"/")+le)),L.push(ee)),1;le=0;var et=Z===""?".":Z+":";if(q(A))for(var Ce=0;Ce<A.length;Ce++)Z=A[Ce],de=et+ye(Z,Ce),le+=Oe(Z,L,$,de,ee);else if(Ce=x(A),typeof Ce=="function")for(A=Ce.call(A),Ce=0;!(Z=A.next()).done;)Z=Z.value,de=et+ye(Z,Ce++),le+=Oe(Z,L,$,de,ee);else if(de==="object"){if(typeof A.then=="function")return Oe(Lt(A),L,$,Z,ee);throw L=String(A),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.")}return le}function B(A,L,$){if(A==null)return A;var Z=[],ee=0;return Oe(A,Z,"","",function(de){return L.call($,de,ee++)}),Z}function I(A){if(A._status===-1){var L=A._result;L=L(),L.then(function($){(A._status===0||A._status===-1)&&(A._status=1,A._result=$)},function($){(A._status===0||A._status===-1)&&(A._status=2,A._result=$)}),A._status===-1&&(A._status=0,A._result=L)}if(A._status===1)return A._result.default;throw A._result}var F=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var L=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(L))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function xe(){}return ue.Children={map:B,forEach:function(A,L,$){B(A,function(){L.apply(this,arguments)},$)},count:function(A){var L=0;return B(A,function(){L++}),L},toArray:function(A){return B(A,function(L){return L})||[]},only:function(A){if(!we(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},ue.Component=O,ue.Fragment=u,ue.Profiler=s,ue.PureComponent=Q,ue.StrictMode=o,ue.Suspense=g,ue.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=H,ue.__COMPILER_RUNTIME={__proto__:null,c:function(A){return H.H.useMemoCache(A)}},ue.cache=function(A){return function(){return A.apply(null,arguments)}},ue.cloneElement=function(A,L,$){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var Z=M({},A.props),ee=A.key,de=void 0;if(L!=null)for(le in L.ref!==void 0&&(de=void 0),L.key!==void 0&&(ee=""+L.key),L)!J.call(L,le)||le==="key"||le==="__self"||le==="__source"||le==="ref"&&L.ref===void 0||(Z[le]=L[le]);var le=arguments.length-2;if(le===1)Z.children=$;else if(1<le){for(var et=Array(le),Ce=0;Ce<le;Ce++)et[Ce]=arguments[Ce+2];Z.children=et}return re(A.type,ee,void 0,void 0,de,Z)},ue.createContext=function(A){return A={$$typeof:d,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:f,_context:A},A},ue.createElement=function(A,L,$){var Z,ee={},de=null;if(L!=null)for(Z in L.key!==void 0&&(de=""+L.key),L)J.call(L,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(ee[Z]=L[Z]);var le=arguments.length-2;if(le===1)ee.children=$;else if(1<le){for(var et=Array(le),Ce=0;Ce<le;Ce++)et[Ce]=arguments[Ce+2];ee.children=et}if(A&&A.defaultProps)for(Z in le=A.defaultProps,le)ee[Z]===void 0&&(ee[Z]=le[Z]);return re(A,de,void 0,void 0,null,ee)},ue.createRef=function(){return{current:null}},ue.forwardRef=function(A){return{$$typeof:h,render:A}},ue.isValidElement=we,ue.lazy=function(A){return{$$typeof:y,_payload:{_status:-1,_result:A},_init:I}},ue.memo=function(A,L){return{$$typeof:p,type:A,compare:L===void 0?null:L}},ue.startTransition=function(A){var L=H.T,$={};H.T=$;try{var Z=A(),ee=H.S;ee!==null&&ee($,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(xe,F)}catch(de){F(de)}finally{H.T=L}},ue.unstable_useCacheRefresh=function(){return H.H.useCacheRefresh()},ue.use=function(A){return H.H.use(A)},ue.useActionState=function(A,L,$){return H.H.useActionState(A,L,$)},ue.useCallback=function(A,L){return H.H.useCallback(A,L)},ue.useContext=function(A){return H.H.useContext(A)},ue.useDebugValue=function(){},ue.useDeferredValue=function(A,L){return H.H.useDeferredValue(A,L)},ue.useEffect=function(A,L,$){var Z=H.H;if(typeof $=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(A,L)},ue.useId=function(){return H.H.useId()},ue.useImperativeHandle=function(A,L,$){return H.H.useImperativeHandle(A,L,$)},ue.useInsertionEffect=function(A,L){return H.H.useInsertionEffect(A,L)},ue.useLayoutEffect=function(A,L){return H.H.useLayoutEffect(A,L)},ue.useMemo=function(A,L){return H.H.useMemo(A,L)},ue.useOptimistic=function(A,L){return H.H.useOptimistic(A,L)},ue.useReducer=function(A,L,$){return H.H.useReducer(A,L,$)},ue.useRef=function(A){return H.H.useRef(A)},ue.useState=function(A){return H.H.useState(A)},ue.useSyncExternalStore=function(A,L,$){return H.H.useSyncExternalStore(A,L,$)},ue.useTransition=function(){return H.H.useTransition()},ue.version="19.1.0",ue}var rp;function Vi(){return rp||(rp=1,so.exports=Yb()),so.exports}var V=Vi();const He=eg(V);var fo={exports:{}},tr={},mo={exports:{}},po={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ip;function qb(){return ip||(ip=1,function(l){function r(B,I){var F=B.length;B.push(I);e:for(;0<F;){var xe=F-1>>>1,A=B[xe];if(0<s(A,I))B[xe]=I,B[F]=A,F=xe;else break e}}function u(B){return B.length===0?null:B[0]}function o(B){if(B.length===0)return null;var I=B[0],F=B.pop();if(F!==I){B[0]=F;e:for(var xe=0,A=B.length,L=A>>>1;xe<L;){var $=2*(xe+1)-1,Z=B[$],ee=$+1,de=B[ee];if(0>s(Z,F))ee<A&&0>s(de,Z)?(B[xe]=de,B[ee]=F,xe=ee):(B[xe]=Z,B[$]=F,xe=$);else if(ee<A&&0>s(de,F))B[xe]=de,B[ee]=F,xe=ee;else break e}}return I}function s(B,I){var F=B.sortIndex-I.sortIndex;return F!==0?F:B.id-I.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();l.unstable_now=function(){return d.now()-h}}var g=[],p=[],y=1,v=null,x=3,D=!1,M=!1,z=!1,O=!1,Y=typeof setTimeout=="function"?setTimeout:null,Q=typeof clearTimeout=="function"?clearTimeout:null,K=typeof setImmediate<"u"?setImmediate:null;function q(B){for(var I=u(p);I!==null;){if(I.callback===null)o(p);else if(I.startTime<=B)o(p),I.sortIndex=I.expirationTime,r(g,I);else break;I=u(p)}}function H(B){if(z=!1,q(B),!M)if(u(g)!==null)M=!0,J||(J=!0,ye());else{var I=u(p);I!==null&&Oe(H,I.startTime-B)}}var J=!1,re=-1,ie=5,we=-1;function ge(){return O?!0:!(l.unstable_now()-we<ie)}function ve(){if(O=!1,J){var B=l.unstable_now();we=B;var I=!0;try{e:{M=!1,z&&(z=!1,Q(re),re=-1),D=!0;var F=x;try{t:{for(q(B),v=u(g);v!==null&&!(v.expirationTime>B&&ge());){var xe=v.callback;if(typeof xe=="function"){v.callback=null,x=v.priorityLevel;var A=xe(v.expirationTime<=B);if(B=l.unstable_now(),typeof A=="function"){v.callback=A,q(B),I=!0;break t}v===u(g)&&o(g),q(B)}else o(g);v=u(g)}if(v!==null)I=!0;else{var L=u(p);L!==null&&Oe(H,L.startTime-B),I=!1}}break e}finally{v=null,x=F,D=!1}I=void 0}}finally{I?ye():J=!1}}}var ye;if(typeof K=="function")ye=function(){K(ve)};else if(typeof MessageChannel<"u"){var $e=new MessageChannel,Lt=$e.port2;$e.port1.onmessage=ve,ye=function(){Lt.postMessage(null)}}else ye=function(){Y(ve,0)};function Oe(B,I){re=Y(function(){B(l.unstable_now())},I)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(B){B.callback=null},l.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ie=0<B?Math.floor(1e3/B):5},l.unstable_getCurrentPriorityLevel=function(){return x},l.unstable_next=function(B){switch(x){case 1:case 2:case 3:var I=3;break;default:I=x}var F=x;x=I;try{return B()}finally{x=F}},l.unstable_requestPaint=function(){O=!0},l.unstable_runWithPriority=function(B,I){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var F=x;x=B;try{return I()}finally{x=F}},l.unstable_scheduleCallback=function(B,I,F){var xe=l.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?xe+F:xe):F=xe,B){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=F+A,B={id:y++,callback:I,priorityLevel:B,startTime:F,expirationTime:A,sortIndex:-1},F>xe?(B.sortIndex=F,r(p,B),u(g)===null&&B===u(p)&&(z?(Q(re),re=-1):z=!0,Oe(H,F-xe))):(B.sortIndex=A,r(g,B),M||D||(M=!0,J||(J=!0,ye()))),B},l.unstable_shouldYield=ge,l.unstable_wrapCallback=function(B){var I=x;return function(){var F=x;x=I;try{return B.apply(this,arguments)}finally{x=F}}}}(po)),po}var up;function Qb(){return up||(up=1,mo.exports=qb()),mo.exports}var go={exports:{}},at={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cp;function Vb(){if(cp)return at;cp=1;var l=Vi();function r(g){var p="https://react.dev/errors/"+g;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)p+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+g+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(g,p,y){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:v==null?null:""+v,children:g,containerInfo:p,implementation:y}}var d=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(g,p){if(g==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return at.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,at.createPortal=function(g,p){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(r(299));return f(g,p,null,y)},at.flushSync=function(g){var p=d.T,y=o.p;try{if(d.T=null,o.p=2,g)return g()}finally{d.T=p,o.p=y,o.d.f()}},at.preconnect=function(g,p){typeof g=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,o.d.C(g,p))},at.prefetchDNS=function(g){typeof g=="string"&&o.d.D(g)},at.preinit=function(g,p){if(typeof g=="string"&&p&&typeof p.as=="string"){var y=p.as,v=h(y,p.crossOrigin),x=typeof p.integrity=="string"?p.integrity:void 0,D=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;y==="style"?o.d.S(g,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:v,integrity:x,fetchPriority:D}):y==="script"&&o.d.X(g,{crossOrigin:v,integrity:x,fetchPriority:D,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},at.preinitModule=function(g,p){if(typeof g=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var y=h(p.as,p.crossOrigin);o.d.M(g,{crossOrigin:y,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&o.d.M(g)},at.preload=function(g,p){if(typeof g=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var y=p.as,v=h(y,p.crossOrigin);o.d.L(g,y,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},at.preloadModule=function(g,p){if(typeof g=="string")if(p){var y=h(p.as,p.crossOrigin);o.d.m(g,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:y,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else o.d.m(g)},at.requestFormReset=function(g){o.d.r(g)},at.unstable_batchedUpdates=function(g,p){return g(p)},at.useFormState=function(g,p,y){return d.H.useFormState(g,p,y)},at.useFormStatus=function(){return d.H.useHostTransitionStatus()},at.version="19.1.0",at}var op;function tg(){if(op)return go.exports;op=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(r){console.error(r)}}return l(),go.exports=Vb(),go.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sp;function Xb(){if(sp)return tr;sp=1;var l=Qb(),r=Vi(),u=tg();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(o(188))}function g(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,a=t;;){var i=n.return;if(i===null)break;var c=i.alternate;if(c===null){if(a=i.return,a!==null){n=a;continue}break}if(i.child===c.child){for(c=i.child;c;){if(c===n)return h(i),e;if(c===a)return h(i),t;c=c.sibling}throw Error(o(188))}if(n.return!==a.return)n=i,a=c;else{for(var m=!1,b=i.child;b;){if(b===n){m=!0,n=i,a=c;break}if(b===a){m=!0,a=i,n=c;break}b=b.sibling}if(!m){for(b=c.child;b;){if(b===n){m=!0,n=c,a=i;break}if(b===a){m=!0,a=c,n=i;break}b=b.sibling}if(!m)throw Error(o(189))}}if(n.alternate!==a)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,v=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),D=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),O=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),Q=Symbol.for("react.consumer"),K=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),J=Symbol.for("react.suspense_list"),re=Symbol.for("react.memo"),ie=Symbol.for("react.lazy"),we=Symbol.for("react.activity"),ge=Symbol.for("react.memo_cache_sentinel"),ve=Symbol.iterator;function ye(e){return e===null||typeof e!="object"?null:(e=ve&&e[ve]||e["@@iterator"],typeof e=="function"?e:null)}var $e=Symbol.for("react.client.reference");function Lt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===$e?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case O:return"Profiler";case z:return"StrictMode";case H:return"Suspense";case J:return"SuspenseList";case we:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case D:return"Portal";case K:return(e.displayName||"Context")+".Provider";case Q:return(e._context.displayName||"Context")+".Consumer";case q:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case re:return t=e.displayName||null,t!==null?t:Lt(e.type)||"Memo";case ie:t=e._payload,e=e._init;try{return Lt(e(t))}catch{}}return null}var Oe=Array.isArray,B=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F={pending:!1,data:null,method:null,action:null},xe=[],A=-1;function L(e){return{current:e}}function $(e){0>A||(e.current=xe[A],xe[A]=null,A--)}function Z(e,t){A++,xe[A]=e.current,e.current=t}var ee=L(null),de=L(null),le=L(null),et=L(null);function Ce(e,t){switch(Z(le,t),Z(de,e),Z(ee,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Mm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Mm(t),e=_m(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}$(ee),Z(ee,e)}function pt(){$(ee),$(de),$(le)}function Vn(e){e.memoizedState!==null&&Z(et,e);var t=ee.current,n=_m(t,e.type);t!==n&&(Z(de,e),Z(ee,n))}function na(e){de.current===e&&($(ee),$(de)),et.current===e&&($(et),$a._currentValue=F)}var tt=Object.prototype.hasOwnProperty,Ft=l.unstable_scheduleCallback,Wi=l.unstable_cancelCallback,hh=l.unstable_shouldYield,bh=l.unstable_requestPaint,Xt=l.unstable_now,vh=l.unstable_getCurrentPriorityLevel,os=l.unstable_ImmediatePriority,ss=l.unstable_UserBlockingPriority,hr=l.unstable_NormalPriority,yh=l.unstable_LowPriority,fs=l.unstable_IdlePriority,xh=l.log,Sh=l.unstable_setDisableYieldValue,la=null,gt=null;function hn(e){if(typeof xh=="function"&&Sh(e),gt&&typeof gt.setStrictMode=="function")try{gt.setStrictMode(la,e)}catch{}}var ht=Math.clz32?Math.clz32:Ah,Eh=Math.log,Dh=Math.LN2;function Ah(e){return e>>>=0,e===0?32:31-(Eh(e)/Dh|0)|0}var br=256,vr=4194304;function Xn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function yr(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var i=0,c=e.suspendedLanes,m=e.pingedLanes;e=e.warmLanes;var b=a&134217727;return b!==0?(a=b&~c,a!==0?i=Xn(a):(m&=b,m!==0?i=Xn(m):n||(n=b&~e,n!==0&&(i=Xn(n))))):(b=a&~c,b!==0?i=Xn(b):m!==0?i=Xn(m):n||(n=a&~e,n!==0&&(i=Xn(n)))),i===0?0:t!==0&&t!==i&&(t&c)===0&&(c=i&-i,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:i}function aa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Nh(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ds(){var e=br;return br<<=1,(br&4194048)===0&&(br=256),e}function ms(){var e=vr;return vr<<=1,(vr&62914560)===0&&(vr=4194304),e}function Pi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ra(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Th(e,t,n,a,i,c){var m=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var b=e.entanglements,E=e.expirationTimes,C=e.hiddenUpdates;for(n=m&~n;0<n;){var j=31-ht(n),G=1<<j;b[j]=0,E[j]=-1;var _=C[j];if(_!==null)for(C[j]=null,j=0;j<_.length;j++){var w=_[j];w!==null&&(w.lane&=-536870913)}n&=~G}a!==0&&ps(e,a,0),c!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=c&~(m&~t))}function ps(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-ht(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function gs(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-ht(n),i=1<<a;i&t|e[a]&t&&(e[a]|=t),n&=~i}}function Fi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function eu(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function hs(){var e=I.p;return e!==0?e:(e=window.event,e===void 0?32:km(e.type))}function Oh(e,t){var n=I.p;try{return I.p=e,t()}finally{I.p=n}}var bn=Math.random().toString(36).slice(2),nt="__reactFiber$"+bn,ot="__reactProps$"+bn,fl="__reactContainer$"+bn,tu="__reactEvents$"+bn,Rh="__reactListeners$"+bn,Ch="__reactHandles$"+bn,bs="__reactResources$"+bn,ia="__reactMarker$"+bn;function nu(e){delete e[nt],delete e[ot],delete e[tu],delete e[Rh],delete e[Ch]}function dl(e){var t=e[nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[fl]||n[nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=jm(e);e!==null;){if(n=e[nt])return n;e=jm(e)}return t}e=n,n=e.parentNode}return null}function ml(e){if(e=e[nt]||e[fl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ua(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function pl(e){var t=e[bs];return t||(t=e[bs]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[ia]=!0}var vs=new Set,ys={};function Zn(e,t){gl(e,t),gl(e+"Capture",t)}function gl(e,t){for(ys[e]=t,e=0;e<t.length;e++)vs.add(t[e])}var Mh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),xs={},Ss={};function _h(e){return tt.call(Ss,e)?!0:tt.call(xs,e)?!1:Mh.test(e)?Ss[e]=!0:(xs[e]=!0,!1)}function xr(e,t,n){if(_h(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Sr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function en(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var lu,Es;function hl(e){if(lu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);lu=t&&t[1]||"",Es=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+lu+e+Es}var au=!1;function ru(e,t){if(!e||au)return"";au=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var G=function(){throw Error()};if(Object.defineProperty(G.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(G,[])}catch(w){var _=w}Reflect.construct(e,[],G)}else{try{G.call()}catch(w){_=w}e.call(G.prototype)}}else{try{throw Error()}catch(w){_=w}(G=e())&&typeof G.catch=="function"&&G.catch(function(){})}}catch(w){if(w&&_&&typeof w.stack=="string")return[w.stack,_.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=a.DetermineComponentFrameRoot(),m=c[0],b=c[1];if(m&&b){var E=m.split(`
`),C=b.split(`
`);for(i=a=0;a<E.length&&!E[a].includes("DetermineComponentFrameRoot");)a++;for(;i<C.length&&!C[i].includes("DetermineComponentFrameRoot");)i++;if(a===E.length||i===C.length)for(a=E.length-1,i=C.length-1;1<=a&&0<=i&&E[a]!==C[i];)i--;for(;1<=a&&0<=i;a--,i--)if(E[a]!==C[i]){if(a!==1||i!==1)do if(a--,i--,0>i||E[a]!==C[i]){var j=`
`+E[a].replace(" at new "," at ");return e.displayName&&j.includes("<anonymous>")&&(j=j.replace("<anonymous>",e.displayName)),j}while(1<=a&&0<=i);break}}}finally{au=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?hl(n):""}function wh(e){switch(e.tag){case 26:case 27:case 5:return hl(e.type);case 16:return hl("Lazy");case 13:return hl("Suspense");case 19:return hl("SuspenseList");case 0:case 15:return ru(e.type,!1);case 11:return ru(e.type.render,!1);case 1:return ru(e.type,!0);case 31:return hl("Activity");default:return""}}function Ds(e){try{var t="";do t+=wh(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Ot(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function As(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function zh(e){var t=As(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(m){a=""+m,c.call(this,m)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(m){a=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Er(e){e._valueTracker||(e._valueTracker=zh(e))}function Ns(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=As(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function Dr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Bh=/[\n"\\]/g;function Rt(e){return e.replace(Bh,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function iu(e,t,n,a,i,c,m,b){e.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.type=m:e.removeAttribute("type"),t!=null?m==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Ot(t)):e.value!==""+Ot(t)&&(e.value=""+Ot(t)):m!=="submit"&&m!=="reset"||e.removeAttribute("value"),t!=null?uu(e,m,Ot(t)):n!=null?uu(e,m,Ot(n)):a!=null&&e.removeAttribute("value"),i==null&&c!=null&&(e.defaultChecked=!!c),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+Ot(b):e.removeAttribute("name")}function Ts(e,t,n,a,i,c,m,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+Ot(n):"",t=t!=null?""+Ot(t):n,b||t===e.value||(e.value=t),e.defaultValue=t}a=a??i,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=b?e.checked:!!a,e.defaultChecked=!!a,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(e.name=m)}function uu(e,t,n){t==="number"&&Dr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bl(e,t,n,a){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&a&&(e[n].defaultSelected=!0)}else{for(n=""+Ot(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,a&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Os(e,t,n){if(t!=null&&(t=""+Ot(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Ot(n):""}function Rs(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(o(92));if(Oe(a)){if(1<a.length)throw Error(o(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=Ot(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function vl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var jh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Cs(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||jh.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ms(e,t,n){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var i in t)a=t[i],t.hasOwnProperty(i)&&n[i]!==a&&Cs(e,i,a)}else for(var c in t)t.hasOwnProperty(c)&&Cs(e,c,t[c])}function cu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Uh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Gh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ar(e){return Gh.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ou=null;function su(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var yl=null,xl=null;function _s(e){var t=ml(e);if(t&&(e=t.stateNode)){var n=e[ot]||null;e:switch(e=t.stateNode,t.type){case"input":if(iu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Rt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var i=a[ot]||null;if(!i)throw Error(o(90));iu(a,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&Ns(a)}break e;case"textarea":Os(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&bl(e,!!n.multiple,t,!1)}}}var fu=!1;function ws(e,t,n){if(fu)return e(t,n);fu=!0;try{var a=e(t);return a}finally{if(fu=!1,(yl!==null||xl!==null)&&(oi(),yl&&(t=yl,e=xl,xl=yl=null,_s(t),e)))for(t=0;t<e.length;t++)_s(e[t])}}function ca(e,t){var n=e.stateNode;if(n===null)return null;var a=n[ot]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var tn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),du=!1;if(tn)try{var oa={};Object.defineProperty(oa,"passive",{get:function(){du=!0}}),window.addEventListener("test",oa,oa),window.removeEventListener("test",oa,oa)}catch{du=!1}var vn=null,mu=null,Nr=null;function zs(){if(Nr)return Nr;var e,t=mu,n=t.length,a,i="value"in vn?vn.value:vn.textContent,c=i.length;for(e=0;e<n&&t[e]===i[e];e++);var m=n-e;for(a=1;a<=m&&t[n-a]===i[c-a];a++);return Nr=i.slice(e,1<a?1-a:void 0)}function Tr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Or(){return!0}function Bs(){return!1}function st(e){function t(n,a,i,c,m){this._reactName=n,this._targetInst=i,this.type=a,this.nativeEvent=c,this.target=m,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(n=e[b],this[b]=n?n(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Or:Bs,this.isPropagationStopped=Bs,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Or)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Or)},persist:function(){},isPersistent:Or}),t}var In={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Rr=st(In),sa=y({},In,{view:0,detail:0}),Hh=st(sa),pu,gu,fa,Cr=y({},sa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fa&&(fa&&e.type==="mousemove"?(pu=e.screenX-fa.screenX,gu=e.screenY-fa.screenY):gu=pu=0,fa=e),pu)},movementY:function(e){return"movementY"in e?e.movementY:gu}}),js=st(Cr),Lh=y({},Cr,{dataTransfer:0}),Yh=st(Lh),qh=y({},sa,{relatedTarget:0}),hu=st(qh),Qh=y({},In,{animationName:0,elapsedTime:0,pseudoElement:0}),Vh=st(Qh),Xh=y({},In,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Zh=st(Xh),Ih=y({},In,{data:0}),Us=st(Ih),Kh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$h={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=$h[e])?!!t[e]:!1}function bu(){return kh}var Wh=y({},sa,{key:function(e){if(e.key){var t=Kh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Tr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bu,charCode:function(e){return e.type==="keypress"?Tr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Tr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ph=st(Wh),Fh=y({},Cr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gs=st(Fh),e0=y({},sa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bu}),t0=st(e0),n0=y({},In,{propertyName:0,elapsedTime:0,pseudoElement:0}),l0=st(n0),a0=y({},Cr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),r0=st(a0),i0=y({},In,{newState:0,oldState:0}),u0=st(i0),c0=[9,13,27,32],vu=tn&&"CompositionEvent"in window,da=null;tn&&"documentMode"in document&&(da=document.documentMode);var o0=tn&&"TextEvent"in window&&!da,Hs=tn&&(!vu||da&&8<da&&11>=da),Ls=" ",Ys=!1;function qs(e,t){switch(e){case"keyup":return c0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Qs(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sl=!1;function s0(e,t){switch(e){case"compositionend":return Qs(t);case"keypress":return t.which!==32?null:(Ys=!0,Ls);case"textInput":return e=t.data,e===Ls&&Ys?null:e;default:return null}}function f0(e,t){if(Sl)return e==="compositionend"||!vu&&qs(e,t)?(e=zs(),Nr=mu=vn=null,Sl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Hs&&t.locale!=="ko"?null:t.data;default:return null}}var d0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!d0[e.type]:t==="textarea"}function Xs(e,t,n,a){yl?xl?xl.push(a):xl=[a]:yl=a,t=gi(t,"onChange"),0<t.length&&(n=new Rr("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var ma=null,pa=null;function m0(e){Nm(e,0)}function Mr(e){var t=ua(e);if(Ns(t))return e}function Zs(e,t){if(e==="change")return t}var Is=!1;if(tn){var yu;if(tn){var xu="oninput"in document;if(!xu){var Ks=document.createElement("div");Ks.setAttribute("oninput","return;"),xu=typeof Ks.oninput=="function"}yu=xu}else yu=!1;Is=yu&&(!document.documentMode||9<document.documentMode)}function Js(){ma&&(ma.detachEvent("onpropertychange",$s),pa=ma=null)}function $s(e){if(e.propertyName==="value"&&Mr(pa)){var t=[];Xs(t,pa,e,su(e)),ws(m0,t)}}function p0(e,t,n){e==="focusin"?(Js(),ma=t,pa=n,ma.attachEvent("onpropertychange",$s)):e==="focusout"&&Js()}function g0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Mr(pa)}function h0(e,t){if(e==="click")return Mr(t)}function b0(e,t){if(e==="input"||e==="change")return Mr(t)}function v0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var bt=typeof Object.is=="function"?Object.is:v0;function ga(e,t){if(bt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var i=n[a];if(!tt.call(t,i)||!bt(e[i],t[i]))return!1}return!0}function ks(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ws(e,t){var n=ks(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ks(n)}}function Ps(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ps(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Fs(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Dr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Dr(e.document)}return t}function Su(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var y0=tn&&"documentMode"in document&&11>=document.documentMode,El=null,Eu=null,ha=null,Du=!1;function ef(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Du||El==null||El!==Dr(a)||(a=El,"selectionStart"in a&&Su(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),ha&&ga(ha,a)||(ha=a,a=gi(Eu,"onSelect"),0<a.length&&(t=new Rr("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=El)))}function Kn(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Dl={animationend:Kn("Animation","AnimationEnd"),animationiteration:Kn("Animation","AnimationIteration"),animationstart:Kn("Animation","AnimationStart"),transitionrun:Kn("Transition","TransitionRun"),transitionstart:Kn("Transition","TransitionStart"),transitioncancel:Kn("Transition","TransitionCancel"),transitionend:Kn("Transition","TransitionEnd")},Au={},tf={};tn&&(tf=document.createElement("div").style,"AnimationEvent"in window||(delete Dl.animationend.animation,delete Dl.animationiteration.animation,delete Dl.animationstart.animation),"TransitionEvent"in window||delete Dl.transitionend.transition);function Jn(e){if(Au[e])return Au[e];if(!Dl[e])return e;var t=Dl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in tf)return Au[e]=t[n];return e}var nf=Jn("animationend"),lf=Jn("animationiteration"),af=Jn("animationstart"),x0=Jn("transitionrun"),S0=Jn("transitionstart"),E0=Jn("transitioncancel"),rf=Jn("transitionend"),uf=new Map,Nu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Nu.push("scrollEnd");function Yt(e,t){uf.set(e,t),Zn(t,[e])}var cf=new WeakMap;function Ct(e,t){if(typeof e=="object"&&e!==null){var n=cf.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Ds(t)},cf.set(e,t),t)}return{value:e,source:t,stack:Ds(t)}}var Mt=[],Al=0,Tu=0;function _r(){for(var e=Al,t=Tu=Al=0;t<e;){var n=Mt[t];Mt[t++]=null;var a=Mt[t];Mt[t++]=null;var i=Mt[t];Mt[t++]=null;var c=Mt[t];if(Mt[t++]=null,a!==null&&i!==null){var m=a.pending;m===null?i.next=i:(i.next=m.next,m.next=i),a.pending=i}c!==0&&of(n,i,c)}}function wr(e,t,n,a){Mt[Al++]=e,Mt[Al++]=t,Mt[Al++]=n,Mt[Al++]=a,Tu|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Ou(e,t,n,a){return wr(e,t,n,a),zr(e)}function Nl(e,t){return wr(e,null,null,t),zr(e)}function of(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var i=!1,c=e.return;c!==null;)c.childLanes|=n,a=c.alternate,a!==null&&(a.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(i=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,i&&t!==null&&(i=31-ht(n),e=c.hiddenUpdates,a=e[i],a===null?e[i]=[t]:a.push(t),t.lane=n|536870912),c):null}function zr(e){if(50<qa)throw qa=0,zc=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Tl={};function D0(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function vt(e,t,n,a){return new D0(e,t,n,a)}function Ru(e){return e=e.prototype,!(!e||!e.isReactComponent)}function nn(e,t){var n=e.alternate;return n===null?(n=vt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function sf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Br(e,t,n,a,i,c){var m=0;if(a=e,typeof e=="function")Ru(e)&&(m=1);else if(typeof e=="string")m=Nb(e,n,ee.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case we:return e=vt(31,n,t,i),e.elementType=we,e.lanes=c,e;case M:return $n(n.children,i,c,t);case z:m=8,i|=24;break;case O:return e=vt(12,n,t,i|2),e.elementType=O,e.lanes=c,e;case H:return e=vt(13,n,t,i),e.elementType=H,e.lanes=c,e;case J:return e=vt(19,n,t,i),e.elementType=J,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Y:case K:m=10;break e;case Q:m=9;break e;case q:m=11;break e;case re:m=14;break e;case ie:m=16,a=null;break e}m=29,n=Error(o(130,e===null?"null":typeof e,"")),a=null}return t=vt(m,n,t,i),t.elementType=e,t.type=a,t.lanes=c,t}function $n(e,t,n,a){return e=vt(7,e,a,t),e.lanes=n,e}function Cu(e,t,n){return e=vt(6,e,null,t),e.lanes=n,e}function Mu(e,t,n){return t=vt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ol=[],Rl=0,jr=null,Ur=0,_t=[],wt=0,kn=null,ln=1,an="";function Wn(e,t){Ol[Rl++]=Ur,Ol[Rl++]=jr,jr=e,Ur=t}function ff(e,t,n){_t[wt++]=ln,_t[wt++]=an,_t[wt++]=kn,kn=e;var a=ln;e=an;var i=32-ht(a)-1;a&=~(1<<i),n+=1;var c=32-ht(t)+i;if(30<c){var m=i-i%5;c=(a&(1<<m)-1).toString(32),a>>=m,i-=m,ln=1<<32-ht(t)+i|n<<i|a,an=c+e}else ln=1<<c|n<<i|a,an=e}function _u(e){e.return!==null&&(Wn(e,1),ff(e,1,0))}function wu(e){for(;e===jr;)jr=Ol[--Rl],Ol[Rl]=null,Ur=Ol[--Rl],Ol[Rl]=null;for(;e===kn;)kn=_t[--wt],_t[wt]=null,an=_t[--wt],_t[wt]=null,ln=_t[--wt],_t[wt]=null}var rt=null,Be=null,be=!1,Pn=null,Zt=!1,zu=Error(o(519));function Fn(e){var t=Error(o(418,""));throw ya(Ct(t,e)),zu}function df(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[nt]=e,t[ot]=a,n){case"dialog":fe("cancel",t),fe("close",t);break;case"iframe":case"object":case"embed":fe("load",t);break;case"video":case"audio":for(n=0;n<Va.length;n++)fe(Va[n],t);break;case"source":fe("error",t);break;case"img":case"image":case"link":fe("error",t),fe("load",t);break;case"details":fe("toggle",t);break;case"input":fe("invalid",t),Ts(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Er(t);break;case"select":fe("invalid",t);break;case"textarea":fe("invalid",t),Rs(t,a.value,a.defaultValue,a.children),Er(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||Cm(t.textContent,n)?(a.popover!=null&&(fe("beforetoggle",t),fe("toggle",t)),a.onScroll!=null&&fe("scroll",t),a.onScrollEnd!=null&&fe("scrollend",t),a.onClick!=null&&(t.onclick=hi),t=!0):t=!1,t||Fn(e)}function mf(e){for(rt=e.return;rt;)switch(rt.tag){case 5:case 13:Zt=!1;return;case 27:case 3:Zt=!0;return;default:rt=rt.return}}function ba(e){if(e!==rt)return!1;if(!be)return mf(e),be=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||$c(e.type,e.memoizedProps)),n=!n),n&&Be&&Fn(e),mf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Be=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Be=null}}else t===27?(t=Be,Bn(e.type)?(e=Fc,Fc=null,Be=e):Be=t):Be=rt?Qt(e.stateNode.nextSibling):null;return!0}function va(){Be=rt=null,be=!1}function pf(){var e=Pn;return e!==null&&(mt===null?mt=e:mt.push.apply(mt,e),Pn=null),e}function ya(e){Pn===null?Pn=[e]:Pn.push(e)}var Bu=L(null),el=null,rn=null;function yn(e,t,n){Z(Bu,t._currentValue),t._currentValue=n}function un(e){e._currentValue=Bu.current,$(Bu)}function ju(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function Uu(e,t,n,a){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var c=i.dependencies;if(c!==null){var m=i.child;c=c.firstContext;e:for(;c!==null;){var b=c;c=i;for(var E=0;E<t.length;E++)if(b.context===t[E]){c.lanes|=n,b=c.alternate,b!==null&&(b.lanes|=n),ju(c.return,n,e),a||(m=null);break e}c=b.next}}else if(i.tag===18){if(m=i.return,m===null)throw Error(o(341));m.lanes|=n,c=m.alternate,c!==null&&(c.lanes|=n),ju(m,n,e),m=null}else m=i.child;if(m!==null)m.return=i;else for(m=i;m!==null;){if(m===e){m=null;break}if(i=m.sibling,i!==null){i.return=m.return,m=i;break}m=m.return}i=m}}function xa(e,t,n,a){e=null;for(var i=t,c=!1;i!==null;){if(!c){if((i.flags&524288)!==0)c=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var m=i.alternate;if(m===null)throw Error(o(387));if(m=m.memoizedProps,m!==null){var b=i.type;bt(i.pendingProps.value,m.value)||(e!==null?e.push(b):e=[b])}}else if(i===et.current){if(m=i.alternate,m===null)throw Error(o(387));m.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push($a):e=[$a])}i=i.return}e!==null&&Uu(t,e,n,a),t.flags|=262144}function Gr(e){for(e=e.firstContext;e!==null;){if(!bt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function tl(e){el=e,rn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function lt(e){return gf(el,e)}function Hr(e,t){return el===null&&tl(e),gf(e,t)}function gf(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},rn===null){if(e===null)throw Error(o(308));rn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else rn=rn.next=t;return n}var A0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},N0=l.unstable_scheduleCallback,T0=l.unstable_NormalPriority,qe={$$typeof:K,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Gu(){return{controller:new A0,data:new Map,refCount:0}}function Sa(e){e.refCount--,e.refCount===0&&N0(T0,function(){e.controller.abort()})}var Ea=null,Hu=0,Cl=0,Ml=null;function O0(e,t){if(Ea===null){var n=Ea=[];Hu=0,Cl=Yc(),Ml={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Hu++,t.then(hf,hf),t}function hf(){if(--Hu===0&&Ea!==null){Ml!==null&&(Ml.status="fulfilled");var e=Ea;Ea=null,Cl=0,Ml=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function R0(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(a.status="rejected",a.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),a}var bf=B.S;B.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&O0(e,t),bf!==null&&bf(e,t)};var nl=L(null);function Lu(){var e=nl.current;return e!==null?e:Me.pooledCache}function Lr(e,t){t===null?Z(nl,nl.current):Z(nl,t.pool)}function vf(){var e=Lu();return e===null?null:{parent:qe._currentValue,pool:e}}var Da=Error(o(460)),yf=Error(o(474)),Yr=Error(o(542)),Yu={then:function(){}};function xf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function qr(){}function Sf(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(qr,qr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Df(e),e;default:if(typeof t.status=="string")t.then(qr,qr);else{if(e=Me,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=a}},function(a){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Df(e),e}throw Aa=t,Da}}var Aa=null;function Ef(){if(Aa===null)throw Error(o(459));var e=Aa;return Aa=null,e}function Df(e){if(e===Da||e===Yr)throw Error(o(483))}var xn=!1;function qu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Qu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Sn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function En(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(Ee&2)!==0){var i=a.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),a.pending=t,t=zr(e),of(e,null,n),t}return wr(e,a,t,n),zr(e)}function Na(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,gs(e,n)}}function Vu(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var i=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var m={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?i=c=m:c=c.next=m,n=n.next}while(n!==null);c===null?i=c=t:c=c.next=t}else i=c=t;n={baseState:a.baseState,firstBaseUpdate:i,lastBaseUpdate:c,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Xu=!1;function Ta(){if(Xu){var e=Ml;if(e!==null)throw e}}function Oa(e,t,n,a){Xu=!1;var i=e.updateQueue;xn=!1;var c=i.firstBaseUpdate,m=i.lastBaseUpdate,b=i.shared.pending;if(b!==null){i.shared.pending=null;var E=b,C=E.next;E.next=null,m===null?c=C:m.next=C,m=E;var j=e.alternate;j!==null&&(j=j.updateQueue,b=j.lastBaseUpdate,b!==m&&(b===null?j.firstBaseUpdate=C:b.next=C,j.lastBaseUpdate=E))}if(c!==null){var G=i.baseState;m=0,j=C=E=null,b=c;do{var _=b.lane&-536870913,w=_!==b.lane;if(w?(me&_)===_:(a&_)===_){_!==0&&_===Cl&&(Xu=!0),j!==null&&(j=j.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var ae=e,te=b;_=t;var Te=n;switch(te.tag){case 1:if(ae=te.payload,typeof ae=="function"){G=ae.call(Te,G,_);break e}G=ae;break e;case 3:ae.flags=ae.flags&-65537|128;case 0:if(ae=te.payload,_=typeof ae=="function"?ae.call(Te,G,_):ae,_==null)break e;G=y({},G,_);break e;case 2:xn=!0}}_=b.callback,_!==null&&(e.flags|=64,w&&(e.flags|=8192),w=i.callbacks,w===null?i.callbacks=[_]:w.push(_))}else w={lane:_,tag:b.tag,payload:b.payload,callback:b.callback,next:null},j===null?(C=j=w,E=G):j=j.next=w,m|=_;if(b=b.next,b===null){if(b=i.shared.pending,b===null)break;w=b,b=w.next,w.next=null,i.lastBaseUpdate=w,i.shared.pending=null}}while(!0);j===null&&(E=G),i.baseState=E,i.firstBaseUpdate=C,i.lastBaseUpdate=j,c===null&&(i.shared.lanes=0),Mn|=m,e.lanes=m,e.memoizedState=G}}function Af(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Nf(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Af(n[e],t)}var _l=L(null),Qr=L(0);function Tf(e,t){e=pn,Z(Qr,e),Z(_l,t),pn=e|t.baseLanes}function Zu(){Z(Qr,pn),Z(_l,_l.current)}function Iu(){pn=Qr.current,$(_l),$(Qr)}var Dn=0,ce=null,Ae=null,Le=null,Vr=!1,wl=!1,ll=!1,Xr=0,Ra=0,zl=null,C0=0;function Ue(){throw Error(o(321))}function Ku(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!bt(e[n],t[n]))return!1;return!0}function Ju(e,t,n,a,i,c){return Dn=c,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,B.H=e===null||e.memoizedState===null?od:sd,ll=!1,c=n(a,i),ll=!1,wl&&(c=Rf(t,n,a,i)),Of(e),c}function Of(e){B.H=kr;var t=Ae!==null&&Ae.next!==null;if(Dn=0,Le=Ae=ce=null,Vr=!1,Ra=0,zl=null,t)throw Error(o(300));e===null||Xe||(e=e.dependencies,e!==null&&Gr(e)&&(Xe=!0))}function Rf(e,t,n,a){ce=e;var i=0;do{if(wl&&(zl=null),Ra=0,wl=!1,25<=i)throw Error(o(301));if(i+=1,Le=Ae=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}B.H=U0,c=t(n,a)}while(wl);return c}function M0(){var e=B.H,t=e.useState()[0];return t=typeof t.then=="function"?Ca(t):t,e=e.useState()[0],(Ae!==null?Ae.memoizedState:null)!==e&&(ce.flags|=1024),t}function $u(){var e=Xr!==0;return Xr=0,e}function ku(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Wu(e){if(Vr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Vr=!1}Dn=0,Le=Ae=ce=null,wl=!1,Ra=Xr=0,zl=null}function ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Le===null?ce.memoizedState=Le=e:Le=Le.next=e,Le}function Ye(){if(Ae===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Ae.next;var t=Le===null?ce.memoizedState:Le.next;if(t!==null)Le=t,Ae=e;else{if(e===null)throw ce.alternate===null?Error(o(467)):Error(o(310));Ae=e,e={memoizedState:Ae.memoizedState,baseState:Ae.baseState,baseQueue:Ae.baseQueue,queue:Ae.queue,next:null},Le===null?ce.memoizedState=Le=e:Le=Le.next=e}return Le}function Pu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ca(e){var t=Ra;return Ra+=1,zl===null&&(zl=[]),e=Sf(zl,e,t),t=ce,(Le===null?t.memoizedState:Le.next)===null&&(t=t.alternate,B.H=t===null||t.memoizedState===null?od:sd),e}function Zr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ca(e);if(e.$$typeof===K)return lt(e)}throw Error(o(438,String(e)))}function Fu(e){var t=null,n=ce.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=ce.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Pu(),ce.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=ge;return t.index++,n}function cn(e,t){return typeof t=="function"?t(e):t}function Ir(e){var t=Ye();return ec(t,Ae,e)}function ec(e,t,n){var a=e.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=n;var i=e.baseQueue,c=a.pending;if(c!==null){if(i!==null){var m=i.next;i.next=c.next,c.next=m}t.baseQueue=i=c,a.pending=null}if(c=e.baseState,i===null)e.memoizedState=c;else{t=i.next;var b=m=null,E=null,C=t,j=!1;do{var G=C.lane&-536870913;if(G!==C.lane?(me&G)===G:(Dn&G)===G){var _=C.revertLane;if(_===0)E!==null&&(E=E.next={lane:0,revertLane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),G===Cl&&(j=!0);else if((Dn&_)===_){C=C.next,_===Cl&&(j=!0);continue}else G={lane:0,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},E===null?(b=E=G,m=c):E=E.next=G,ce.lanes|=_,Mn|=_;G=C.action,ll&&n(c,G),c=C.hasEagerState?C.eagerState:n(c,G)}else _={lane:G,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},E===null?(b=E=_,m=c):E=E.next=_,ce.lanes|=G,Mn|=G;C=C.next}while(C!==null&&C!==t);if(E===null?m=c:E.next=b,!bt(c,e.memoizedState)&&(Xe=!0,j&&(n=Ml,n!==null)))throw n;e.memoizedState=c,e.baseState=m,e.baseQueue=E,a.lastRenderedState=c}return i===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function tc(e){var t=Ye(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var a=n.dispatch,i=n.pending,c=t.memoizedState;if(i!==null){n.pending=null;var m=i=i.next;do c=e(c,m.action),m=m.next;while(m!==i);bt(c,t.memoizedState)||(Xe=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,a]}function Cf(e,t,n){var a=ce,i=Ye(),c=be;if(c){if(n===void 0)throw Error(o(407));n=n()}else n=t();var m=!bt((Ae||i).memoizedState,n);m&&(i.memoizedState=n,Xe=!0),i=i.queue;var b=wf.bind(null,a,i,e);if(Ma(2048,8,b,[e]),i.getSnapshot!==t||m||Le!==null&&Le.memoizedState.tag&1){if(a.flags|=2048,Bl(9,Kr(),_f.bind(null,a,i,n,t),null),Me===null)throw Error(o(349));c||(Dn&124)!==0||Mf(a,t,n)}return n}function Mf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t=Pu(),ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function _f(e,t,n,a){t.value=n,t.getSnapshot=a,zf(t)&&Bf(e)}function wf(e,t,n){return n(function(){zf(t)&&Bf(e)})}function zf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!bt(e,n)}catch{return!0}}function Bf(e){var t=Nl(e,2);t!==null&&Dt(t,e,2)}function nc(e){var t=ft();if(typeof e=="function"){var n=e;if(e=n(),ll){hn(!0);try{n()}finally{hn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:e},t}function jf(e,t,n,a){return e.baseState=n,ec(e,Ae,typeof a=="function"?a:cn)}function _0(e,t,n,a,i){if($r(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){c.listeners.push(m)}};B.T!==null?n(!0):c.isTransition=!1,a(c),n=t.pending,n===null?(c.next=t.pending=c,Uf(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Uf(e,t){var n=t.action,a=t.payload,i=e.state;if(t.isTransition){var c=B.T,m={};B.T=m;try{var b=n(i,a),E=B.S;E!==null&&E(m,b),Gf(e,t,b)}catch(C){lc(e,t,C)}finally{B.T=c}}else try{c=n(i,a),Gf(e,t,c)}catch(C){lc(e,t,C)}}function Gf(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){Hf(e,t,a)},function(a){return lc(e,t,a)}):Hf(e,t,n)}function Hf(e,t,n){t.status="fulfilled",t.value=n,Lf(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Uf(e,n)))}function lc(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,Lf(t),t=t.next;while(t!==a)}e.action=null}function Lf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Yf(e,t){return t}function qf(e,t){if(be){var n=Me.formState;if(n!==null){e:{var a=ce;if(be){if(Be){t:{for(var i=Be,c=Zt;i.nodeType!==8;){if(!c){i=null;break t}if(i=Qt(i.nextSibling),i===null){i=null;break t}}c=i.data,i=c==="F!"||c==="F"?i:null}if(i){Be=Qt(i.nextSibling),a=i.data==="F!";break e}}Fn(a)}a=!1}a&&(t=n[0])}}return n=ft(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Yf,lastRenderedState:t},n.queue=a,n=id.bind(null,ce,a),a.dispatch=n,a=nc(!1),c=cc.bind(null,ce,!1,a.queue),a=ft(),i={state:t,dispatch:null,action:e,pending:null},a.queue=i,n=_0.bind(null,ce,i,c,n),i.dispatch=n,a.memoizedState=e,[t,n,!1]}function Qf(e){var t=Ye();return Vf(t,Ae,e)}function Vf(e,t,n){if(t=ec(e,t,Yf)[0],e=Ir(cn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Ca(t)}catch(m){throw m===Da?Yr:m}else a=t;t=Ye();var i=t.queue,c=i.dispatch;return n!==t.memoizedState&&(ce.flags|=2048,Bl(9,Kr(),w0.bind(null,i,n),null)),[a,c,e]}function w0(e,t){e.action=t}function Xf(e){var t=Ye(),n=Ae;if(n!==null)return Vf(t,n,e);Ye(),t=t.memoizedState,n=Ye();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function Bl(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=ce.updateQueue,t===null&&(t=Pu(),ce.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Kr(){return{destroy:void 0,resource:void 0}}function Zf(){return Ye().memoizedState}function Jr(e,t,n,a){var i=ft();a=a===void 0?null:a,ce.flags|=e,i.memoizedState=Bl(1|t,Kr(),n,a)}function Ma(e,t,n,a){var i=Ye();a=a===void 0?null:a;var c=i.memoizedState.inst;Ae!==null&&a!==null&&Ku(a,Ae.memoizedState.deps)?i.memoizedState=Bl(t,c,n,a):(ce.flags|=e,i.memoizedState=Bl(1|t,c,n,a))}function If(e,t){Jr(8390656,8,e,t)}function Kf(e,t){Ma(2048,8,e,t)}function Jf(e,t){return Ma(4,2,e,t)}function $f(e,t){return Ma(4,4,e,t)}function kf(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Wf(e,t,n){n=n!=null?n.concat([e]):null,Ma(4,4,kf.bind(null,t,e),n)}function ac(){}function Pf(e,t){var n=Ye();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&Ku(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function Ff(e,t){var n=Ye();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&Ku(t,a[1]))return a[0];if(a=e(),ll){hn(!0);try{e()}finally{hn(!1)}}return n.memoizedState=[a,t],a}function rc(e,t,n){return n===void 0||(Dn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=nm(),ce.lanes|=e,Mn|=e,n)}function ed(e,t,n,a){return bt(n,t)?n:_l.current!==null?(e=rc(e,n,a),bt(e,t)||(Xe=!0),e):(Dn&42)===0?(Xe=!0,e.memoizedState=n):(e=nm(),ce.lanes|=e,Mn|=e,t)}function td(e,t,n,a,i){var c=I.p;I.p=c!==0&&8>c?c:8;var m=B.T,b={};B.T=b,cc(e,!1,t,n);try{var E=i(),C=B.S;if(C!==null&&C(b,E),E!==null&&typeof E=="object"&&typeof E.then=="function"){var j=R0(E,a);_a(e,t,j,Et(e))}else _a(e,t,a,Et(e))}catch(G){_a(e,t,{then:function(){},status:"rejected",reason:G},Et())}finally{I.p=c,B.T=m}}function z0(){}function ic(e,t,n,a){if(e.tag!==5)throw Error(o(476));var i=nd(e).queue;td(e,i,t,F,n===null?z0:function(){return ld(e),n(a)})}function nd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:F,baseState:F,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:F},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function ld(e){var t=nd(e).next.queue;_a(e,t,{},Et())}function uc(){return lt($a)}function ad(){return Ye().memoizedState}function rd(){return Ye().memoizedState}function B0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Et();e=Sn(n);var a=En(t,e,n);a!==null&&(Dt(a,t,n),Na(a,t,n)),t={cache:Gu()},e.payload=t;return}t=t.return}}function j0(e,t,n){var a=Et();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},$r(e)?ud(t,n):(n=Ou(e,t,n,a),n!==null&&(Dt(n,e,a),cd(n,t,a)))}function id(e,t,n){var a=Et();_a(e,t,n,a)}function _a(e,t,n,a){var i={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if($r(e))ud(t,i);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var m=t.lastRenderedState,b=c(m,n);if(i.hasEagerState=!0,i.eagerState=b,bt(b,m))return wr(e,t,i,0),Me===null&&_r(),!1}catch{}finally{}if(n=Ou(e,t,i,a),n!==null)return Dt(n,e,a),cd(n,t,a),!0}return!1}function cc(e,t,n,a){if(a={lane:2,revertLane:Yc(),action:a,hasEagerState:!1,eagerState:null,next:null},$r(e)){if(t)throw Error(o(479))}else t=Ou(e,n,a,2),t!==null&&Dt(t,e,2)}function $r(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function ud(e,t){wl=Vr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function cd(e,t,n){if((n&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,gs(e,n)}}var kr={readContext:lt,use:Zr,useCallback:Ue,useContext:Ue,useEffect:Ue,useImperativeHandle:Ue,useLayoutEffect:Ue,useInsertionEffect:Ue,useMemo:Ue,useReducer:Ue,useRef:Ue,useState:Ue,useDebugValue:Ue,useDeferredValue:Ue,useTransition:Ue,useSyncExternalStore:Ue,useId:Ue,useHostTransitionStatus:Ue,useFormState:Ue,useActionState:Ue,useOptimistic:Ue,useMemoCache:Ue,useCacheRefresh:Ue},od={readContext:lt,use:Zr,useCallback:function(e,t){return ft().memoizedState=[e,t===void 0?null:t],e},useContext:lt,useEffect:If,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Jr(4194308,4,kf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Jr(4194308,4,e,t)},useInsertionEffect:function(e,t){Jr(4,2,e,t)},useMemo:function(e,t){var n=ft();t=t===void 0?null:t;var a=e();if(ll){hn(!0);try{e()}finally{hn(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=ft();if(n!==void 0){var i=n(t);if(ll){hn(!0);try{n(t)}finally{hn(!1)}}}else i=t;return a.memoizedState=a.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},a.queue=e,e=e.dispatch=j0.bind(null,ce,e),[a.memoizedState,e]},useRef:function(e){var t=ft();return e={current:e},t.memoizedState=e},useState:function(e){e=nc(e);var t=e.queue,n=id.bind(null,ce,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ac,useDeferredValue:function(e,t){var n=ft();return rc(n,e,t)},useTransition:function(){var e=nc(!1);return e=td.bind(null,ce,e.queue,!0,!1),ft().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=ce,i=ft();if(be){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),Me===null)throw Error(o(349));(me&124)!==0||Mf(a,t,n)}i.memoizedState=n;var c={value:n,getSnapshot:t};return i.queue=c,If(wf.bind(null,a,c,e),[e]),a.flags|=2048,Bl(9,Kr(),_f.bind(null,a,c,n,t),null),n},useId:function(){var e=ft(),t=Me.identifierPrefix;if(be){var n=an,a=ln;n=(a&~(1<<32-ht(a)-1)).toString(32)+n,t="«"+t+"R"+n,n=Xr++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=C0++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:uc,useFormState:qf,useActionState:qf,useOptimistic:function(e){var t=ft();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=cc.bind(null,ce,!0,n),n.dispatch=t,[e,t]},useMemoCache:Fu,useCacheRefresh:function(){return ft().memoizedState=B0.bind(null,ce)}},sd={readContext:lt,use:Zr,useCallback:Pf,useContext:lt,useEffect:Kf,useImperativeHandle:Wf,useInsertionEffect:Jf,useLayoutEffect:$f,useMemo:Ff,useReducer:Ir,useRef:Zf,useState:function(){return Ir(cn)},useDebugValue:ac,useDeferredValue:function(e,t){var n=Ye();return ed(n,Ae.memoizedState,e,t)},useTransition:function(){var e=Ir(cn)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:Ca(e),t]},useSyncExternalStore:Cf,useId:ad,useHostTransitionStatus:uc,useFormState:Qf,useActionState:Qf,useOptimistic:function(e,t){var n=Ye();return jf(n,Ae,e,t)},useMemoCache:Fu,useCacheRefresh:rd},U0={readContext:lt,use:Zr,useCallback:Pf,useContext:lt,useEffect:Kf,useImperativeHandle:Wf,useInsertionEffect:Jf,useLayoutEffect:$f,useMemo:Ff,useReducer:tc,useRef:Zf,useState:function(){return tc(cn)},useDebugValue:ac,useDeferredValue:function(e,t){var n=Ye();return Ae===null?rc(n,e,t):ed(n,Ae.memoizedState,e,t)},useTransition:function(){var e=tc(cn)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:Ca(e),t]},useSyncExternalStore:Cf,useId:ad,useHostTransitionStatus:uc,useFormState:Xf,useActionState:Xf,useOptimistic:function(e,t){var n=Ye();return Ae!==null?jf(n,Ae,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Fu,useCacheRefresh:rd},jl=null,wa=0;function Wr(e){var t=wa;return wa+=1,jl===null&&(jl=[]),Sf(jl,e,t)}function za(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Pr(e,t){throw t.$$typeof===v?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function fd(e){var t=e._init;return t(e._payload)}function dd(e){function t(T,N){if(e){var R=T.deletions;R===null?(T.deletions=[N],T.flags|=16):R.push(N)}}function n(T,N){if(!e)return null;for(;N!==null;)t(T,N),N=N.sibling;return null}function a(T){for(var N=new Map;T!==null;)T.key!==null?N.set(T.key,T):N.set(T.index,T),T=T.sibling;return N}function i(T,N){return T=nn(T,N),T.index=0,T.sibling=null,T}function c(T,N,R){return T.index=R,e?(R=T.alternate,R!==null?(R=R.index,R<N?(T.flags|=67108866,N):R):(T.flags|=67108866,N)):(T.flags|=1048576,N)}function m(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function b(T,N,R,U){return N===null||N.tag!==6?(N=Cu(R,T.mode,U),N.return=T,N):(N=i(N,R),N.return=T,N)}function E(T,N,R,U){var k=R.type;return k===M?j(T,N,R.props.children,U,R.key):N!==null&&(N.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===ie&&fd(k)===N.type)?(N=i(N,R.props),za(N,R),N.return=T,N):(N=Br(R.type,R.key,R.props,null,T.mode,U),za(N,R),N.return=T,N)}function C(T,N,R,U){return N===null||N.tag!==4||N.stateNode.containerInfo!==R.containerInfo||N.stateNode.implementation!==R.implementation?(N=Mu(R,T.mode,U),N.return=T,N):(N=i(N,R.children||[]),N.return=T,N)}function j(T,N,R,U,k){return N===null||N.tag!==7?(N=$n(R,T.mode,U,k),N.return=T,N):(N=i(N,R),N.return=T,N)}function G(T,N,R){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return N=Cu(""+N,T.mode,R),N.return=T,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case x:return R=Br(N.type,N.key,N.props,null,T.mode,R),za(R,N),R.return=T,R;case D:return N=Mu(N,T.mode,R),N.return=T,N;case ie:var U=N._init;return N=U(N._payload),G(T,N,R)}if(Oe(N)||ye(N))return N=$n(N,T.mode,R,null),N.return=T,N;if(typeof N.then=="function")return G(T,Wr(N),R);if(N.$$typeof===K)return G(T,Hr(T,N),R);Pr(T,N)}return null}function _(T,N,R,U){var k=N!==null?N.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return k!==null?null:b(T,N,""+R,U);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case x:return R.key===k?E(T,N,R,U):null;case D:return R.key===k?C(T,N,R,U):null;case ie:return k=R._init,R=k(R._payload),_(T,N,R,U)}if(Oe(R)||ye(R))return k!==null?null:j(T,N,R,U,null);if(typeof R.then=="function")return _(T,N,Wr(R),U);if(R.$$typeof===K)return _(T,N,Hr(T,R),U);Pr(T,R)}return null}function w(T,N,R,U,k){if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return T=T.get(R)||null,b(N,T,""+U,k);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case x:return T=T.get(U.key===null?R:U.key)||null,E(N,T,U,k);case D:return T=T.get(U.key===null?R:U.key)||null,C(N,T,U,k);case ie:var oe=U._init;return U=oe(U._payload),w(T,N,R,U,k)}if(Oe(U)||ye(U))return T=T.get(R)||null,j(N,T,U,k,null);if(typeof U.then=="function")return w(T,N,R,Wr(U),k);if(U.$$typeof===K)return w(T,N,R,Hr(N,U),k);Pr(N,U)}return null}function ae(T,N,R,U){for(var k=null,oe=null,W=N,ne=N=0,Ie=null;W!==null&&ne<R.length;ne++){W.index>ne?(Ie=W,W=null):Ie=W.sibling;var he=_(T,W,R[ne],U);if(he===null){W===null&&(W=Ie);break}e&&W&&he.alternate===null&&t(T,W),N=c(he,N,ne),oe===null?k=he:oe.sibling=he,oe=he,W=Ie}if(ne===R.length)return n(T,W),be&&Wn(T,ne),k;if(W===null){for(;ne<R.length;ne++)W=G(T,R[ne],U),W!==null&&(N=c(W,N,ne),oe===null?k=W:oe.sibling=W,oe=W);return be&&Wn(T,ne),k}for(W=a(W);ne<R.length;ne++)Ie=w(W,T,ne,R[ne],U),Ie!==null&&(e&&Ie.alternate!==null&&W.delete(Ie.key===null?ne:Ie.key),N=c(Ie,N,ne),oe===null?k=Ie:oe.sibling=Ie,oe=Ie);return e&&W.forEach(function(Ln){return t(T,Ln)}),be&&Wn(T,ne),k}function te(T,N,R,U){if(R==null)throw Error(o(151));for(var k=null,oe=null,W=N,ne=N=0,Ie=null,he=R.next();W!==null&&!he.done;ne++,he=R.next()){W.index>ne?(Ie=W,W=null):Ie=W.sibling;var Ln=_(T,W,he.value,U);if(Ln===null){W===null&&(W=Ie);break}e&&W&&Ln.alternate===null&&t(T,W),N=c(Ln,N,ne),oe===null?k=Ln:oe.sibling=Ln,oe=Ln,W=Ie}if(he.done)return n(T,W),be&&Wn(T,ne),k;if(W===null){for(;!he.done;ne++,he=R.next())he=G(T,he.value,U),he!==null&&(N=c(he,N,ne),oe===null?k=he:oe.sibling=he,oe=he);return be&&Wn(T,ne),k}for(W=a(W);!he.done;ne++,he=R.next())he=w(W,T,ne,he.value,U),he!==null&&(e&&he.alternate!==null&&W.delete(he.key===null?ne:he.key),N=c(he,N,ne),oe===null?k=he:oe.sibling=he,oe=he);return e&&W.forEach(function(Gb){return t(T,Gb)}),be&&Wn(T,ne),k}function Te(T,N,R,U){if(typeof R=="object"&&R!==null&&R.type===M&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case x:e:{for(var k=R.key;N!==null;){if(N.key===k){if(k=R.type,k===M){if(N.tag===7){n(T,N.sibling),U=i(N,R.props.children),U.return=T,T=U;break e}}else if(N.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===ie&&fd(k)===N.type){n(T,N.sibling),U=i(N,R.props),za(U,R),U.return=T,T=U;break e}n(T,N);break}else t(T,N);N=N.sibling}R.type===M?(U=$n(R.props.children,T.mode,U,R.key),U.return=T,T=U):(U=Br(R.type,R.key,R.props,null,T.mode,U),za(U,R),U.return=T,T=U)}return m(T);case D:e:{for(k=R.key;N!==null;){if(N.key===k)if(N.tag===4&&N.stateNode.containerInfo===R.containerInfo&&N.stateNode.implementation===R.implementation){n(T,N.sibling),U=i(N,R.children||[]),U.return=T,T=U;break e}else{n(T,N);break}else t(T,N);N=N.sibling}U=Mu(R,T.mode,U),U.return=T,T=U}return m(T);case ie:return k=R._init,R=k(R._payload),Te(T,N,R,U)}if(Oe(R))return ae(T,N,R,U);if(ye(R)){if(k=ye(R),typeof k!="function")throw Error(o(150));return R=k.call(R),te(T,N,R,U)}if(typeof R.then=="function")return Te(T,N,Wr(R),U);if(R.$$typeof===K)return Te(T,N,Hr(T,R),U);Pr(T,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,N!==null&&N.tag===6?(n(T,N.sibling),U=i(N,R),U.return=T,T=U):(n(T,N),U=Cu(R,T.mode,U),U.return=T,T=U),m(T)):n(T,N)}return function(T,N,R,U){try{wa=0;var k=Te(T,N,R,U);return jl=null,k}catch(W){if(W===Da||W===Yr)throw W;var oe=vt(29,W,null,T.mode);return oe.lanes=U,oe.return=T,oe}finally{}}}var Ul=dd(!0),md=dd(!1),zt=L(null),It=null;function An(e){var t=e.alternate;Z(Qe,Qe.current&1),Z(zt,e),It===null&&(t===null||_l.current!==null||t.memoizedState!==null)&&(It=e)}function pd(e){if(e.tag===22){if(Z(Qe,Qe.current),Z(zt,e),It===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(It=e)}}else Nn()}function Nn(){Z(Qe,Qe.current),Z(zt,zt.current)}function on(e){$(zt),It===e&&(It=null),$(Qe)}var Qe=L(0);function Fr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Pc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function oc(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var sc={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=Et(),i=Sn(a);i.payload=t,n!=null&&(i.callback=n),t=En(e,i,a),t!==null&&(Dt(t,e,a),Na(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=Et(),i=Sn(a);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=En(e,i,a),t!==null&&(Dt(t,e,a),Na(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Et(),a=Sn(n);a.tag=2,t!=null&&(a.callback=t),t=En(e,a,n),t!==null&&(Dt(t,e,n),Na(t,e,n))}};function gd(e,t,n,a,i,c,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,c,m):t.prototype&&t.prototype.isPureReactComponent?!ga(n,a)||!ga(i,c):!0}function hd(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&sc.enqueueReplaceState(t,t.state,null)}function al(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var ei=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function bd(e){ei(e)}function vd(e){console.error(e)}function yd(e){ei(e)}function ti(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function xd(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function fc(e,t,n){return n=Sn(n),n.tag=3,n.payload={element:null},n.callback=function(){ti(e,t)},n}function Sd(e){return e=Sn(e),e.tag=3,e}function Ed(e,t,n,a){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var c=a.value;e.payload=function(){return i(c)},e.callback=function(){xd(t,n,a)}}var m=n.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(e.callback=function(){xd(t,n,a),typeof i!="function"&&(_n===null?_n=new Set([this]):_n.add(this));var b=a.stack;this.componentDidCatch(a.value,{componentStack:b!==null?b:""})})}function G0(e,t,n,a,i){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&xa(t,n,i,!0),n=zt.current,n!==null){switch(n.tag){case 13:return It===null?jc():n.alternate===null&&je===0&&(je=3),n.flags&=-257,n.flags|=65536,n.lanes=i,a===Yu?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),Gc(e,a,i)),!1;case 22:return n.flags|=65536,a===Yu?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),Gc(e,a,i)),!1}throw Error(o(435,n.tag))}return Gc(e,a,i),jc(),!1}if(be)return t=zt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,a!==zu&&(e=Error(o(422),{cause:a}),ya(Ct(e,n)))):(a!==zu&&(t=Error(o(423),{cause:a}),ya(Ct(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,a=Ct(a,n),i=fc(e.stateNode,a,i),Vu(e,i),je!==4&&(je=2)),!1;var c=Error(o(520),{cause:a});if(c=Ct(c,n),Ya===null?Ya=[c]:Ya.push(c),je!==4&&(je=2),t===null)return!0;a=Ct(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=fc(n.stateNode,a,e),Vu(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(_n===null||!_n.has(c))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Sd(i),Ed(i,e,n,a),Vu(n,i),!1}n=n.return}while(n!==null);return!1}var Dd=Error(o(461)),Xe=!1;function ke(e,t,n,a){t.child=e===null?md(t,null,n,a):Ul(t,e.child,n,a)}function Ad(e,t,n,a,i){n=n.render;var c=t.ref;if("ref"in a){var m={};for(var b in a)b!=="ref"&&(m[b]=a[b])}else m=a;return tl(t),a=Ju(e,t,n,m,c,i),b=$u(),e!==null&&!Xe?(ku(e,t,i),sn(e,t,i)):(be&&b&&_u(t),t.flags|=1,ke(e,t,a,i),t.child)}function Nd(e,t,n,a,i){if(e===null){var c=n.type;return typeof c=="function"&&!Ru(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,Td(e,t,c,a,i)):(e=Br(n.type,null,a,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!yc(e,i)){var m=c.memoizedProps;if(n=n.compare,n=n!==null?n:ga,n(m,a)&&e.ref===t.ref)return sn(e,t,i)}return t.flags|=1,e=nn(c,a),e.ref=t.ref,e.return=t,t.child=e}function Td(e,t,n,a,i){if(e!==null){var c=e.memoizedProps;if(ga(c,a)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=a=c,yc(e,i))(e.flags&131072)!==0&&(Xe=!0);else return t.lanes=e.lanes,sn(e,t,i)}return dc(e,t,n,a,i)}function Od(e,t,n){var a=t.pendingProps,i=a.children,c=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=c!==null?c.baseLanes|n:n,e!==null){for(i=t.child=e.child,c=0;i!==null;)c=c|i.lanes|i.childLanes,i=i.sibling;t.childLanes=c&~a}else t.childLanes=0,t.child=null;return Rd(e,t,a,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Lr(t,c!==null?c.cachePool:null),c!==null?Tf(t,c):Zu(),pd(t);else return t.lanes=t.childLanes=536870912,Rd(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Lr(t,c.cachePool),Tf(t,c),Nn(),t.memoizedState=null):(e!==null&&Lr(t,null),Zu(),Nn());return ke(e,t,i,n),t.child}function Rd(e,t,n,a){var i=Lu();return i=i===null?null:{parent:qe._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&Lr(t,null),Zu(),pd(t),e!==null&&xa(e,t,a,!0),null}function ni(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function dc(e,t,n,a,i){return tl(t),n=Ju(e,t,n,a,void 0,i),a=$u(),e!==null&&!Xe?(ku(e,t,i),sn(e,t,i)):(be&&a&&_u(t),t.flags|=1,ke(e,t,n,i),t.child)}function Cd(e,t,n,a,i,c){return tl(t),t.updateQueue=null,n=Rf(t,a,n,i),Of(e),a=$u(),e!==null&&!Xe?(ku(e,t,c),sn(e,t,c)):(be&&a&&_u(t),t.flags|=1,ke(e,t,n,c),t.child)}function Md(e,t,n,a,i){if(tl(t),t.stateNode===null){var c=Tl,m=n.contextType;typeof m=="object"&&m!==null&&(c=lt(m)),c=new n(a,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=sc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=a,c.state=t.memoizedState,c.refs={},qu(t),m=n.contextType,c.context=typeof m=="object"&&m!==null?lt(m):Tl,c.state=t.memoizedState,m=n.getDerivedStateFromProps,typeof m=="function"&&(oc(t,n,m,a),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(m=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),m!==c.state&&sc.enqueueReplaceState(c,c.state,null),Oa(t,a,c,i),Ta(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){c=t.stateNode;var b=t.memoizedProps,E=al(n,b);c.props=E;var C=c.context,j=n.contextType;m=Tl,typeof j=="object"&&j!==null&&(m=lt(j));var G=n.getDerivedStateFromProps;j=typeof G=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,j||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||C!==m)&&hd(t,c,a,m),xn=!1;var _=t.memoizedState;c.state=_,Oa(t,a,c,i),Ta(),C=t.memoizedState,b||_!==C||xn?(typeof G=="function"&&(oc(t,n,G,a),C=t.memoizedState),(E=xn||gd(t,n,E,a,_,C,m))?(j||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=C),c.props=a,c.state=C,c.context=m,a=E):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{c=t.stateNode,Qu(e,t),m=t.memoizedProps,j=al(n,m),c.props=j,G=t.pendingProps,_=c.context,C=n.contextType,E=Tl,typeof C=="object"&&C!==null&&(E=lt(C)),b=n.getDerivedStateFromProps,(C=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(m!==G||_!==E)&&hd(t,c,a,E),xn=!1,_=t.memoizedState,c.state=_,Oa(t,a,c,i),Ta();var w=t.memoizedState;m!==G||_!==w||xn||e!==null&&e.dependencies!==null&&Gr(e.dependencies)?(typeof b=="function"&&(oc(t,n,b,a),w=t.memoizedState),(j=xn||gd(t,n,j,a,_,w,E)||e!==null&&e.dependencies!==null&&Gr(e.dependencies))?(C||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(a,w,E),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(a,w,E)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||m===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=w),c.props=a,c.state=w,c.context=E,a=j):(typeof c.componentDidUpdate!="function"||m===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),a=!1)}return c=a,ni(e,t),a=(t.flags&128)!==0,c||a?(c=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&a?(t.child=Ul(t,e.child,null,i),t.child=Ul(t,null,n,i)):ke(e,t,n,i),t.memoizedState=c.state,e=t.child):e=sn(e,t,i),e}function _d(e,t,n,a){return va(),t.flags|=256,ke(e,t,n,a),t.child}var mc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function pc(e){return{baseLanes:e,cachePool:vf()}}function gc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Bt),e}function wd(e,t,n){var a=t.pendingProps,i=!1,c=(t.flags&128)!==0,m;if((m=c)||(m=e!==null&&e.memoizedState===null?!1:(Qe.current&2)!==0),m&&(i=!0,t.flags&=-129),m=(t.flags&32)!==0,t.flags&=-33,e===null){if(be){if(i?An(t):Nn(),be){var b=Be,E;if(E=b){e:{for(E=b,b=Zt;E.nodeType!==8;){if(!b){b=null;break e}if(E=Qt(E.nextSibling),E===null){b=null;break e}}b=E}b!==null?(t.memoizedState={dehydrated:b,treeContext:kn!==null?{id:ln,overflow:an}:null,retryLane:536870912,hydrationErrors:null},E=vt(18,null,null,0),E.stateNode=b,E.return=t,t.child=E,rt=t,Be=null,E=!0):E=!1}E||Fn(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return Pc(b)?t.lanes=32:t.lanes=536870912,null;on(t)}return b=a.children,a=a.fallback,i?(Nn(),i=t.mode,b=li({mode:"hidden",children:b},i),a=$n(a,i,n,null),b.return=t,a.return=t,b.sibling=a,t.child=b,i=t.child,i.memoizedState=pc(n),i.childLanes=gc(e,m,n),t.memoizedState=mc,a):(An(t),hc(t,b))}if(E=e.memoizedState,E!==null&&(b=E.dehydrated,b!==null)){if(c)t.flags&256?(An(t),t.flags&=-257,t=bc(e,t,n)):t.memoizedState!==null?(Nn(),t.child=e.child,t.flags|=128,t=null):(Nn(),i=a.fallback,b=t.mode,a=li({mode:"visible",children:a.children},b),i=$n(i,b,n,null),i.flags|=2,a.return=t,i.return=t,a.sibling=i,t.child=a,Ul(t,e.child,null,n),a=t.child,a.memoizedState=pc(n),a.childLanes=gc(e,m,n),t.memoizedState=mc,t=i);else if(An(t),Pc(b)){if(m=b.nextSibling&&b.nextSibling.dataset,m)var C=m.dgst;m=C,a=Error(o(419)),a.stack="",a.digest=m,ya({value:a,source:null,stack:null}),t=bc(e,t,n)}else if(Xe||xa(e,t,n,!1),m=(n&e.childLanes)!==0,Xe||m){if(m=Me,m!==null&&(a=n&-n,a=(a&42)!==0?1:Fi(a),a=(a&(m.suspendedLanes|n))!==0?0:a,a!==0&&a!==E.retryLane))throw E.retryLane=a,Nl(e,a),Dt(m,e,a),Dd;b.data==="$?"||jc(),t=bc(e,t,n)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=E.treeContext,Be=Qt(b.nextSibling),rt=t,be=!0,Pn=null,Zt=!1,e!==null&&(_t[wt++]=ln,_t[wt++]=an,_t[wt++]=kn,ln=e.id,an=e.overflow,kn=t),t=hc(t,a.children),t.flags|=4096);return t}return i?(Nn(),i=a.fallback,b=t.mode,E=e.child,C=E.sibling,a=nn(E,{mode:"hidden",children:a.children}),a.subtreeFlags=E.subtreeFlags&65011712,C!==null?i=nn(C,i):(i=$n(i,b,n,null),i.flags|=2),i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,b=e.child.memoizedState,b===null?b=pc(n):(E=b.cachePool,E!==null?(C=qe._currentValue,E=E.parent!==C?{parent:C,pool:C}:E):E=vf(),b={baseLanes:b.baseLanes|n,cachePool:E}),i.memoizedState=b,i.childLanes=gc(e,m,n),t.memoizedState=mc,a):(An(t),n=e.child,e=n.sibling,n=nn(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(m=t.deletions,m===null?(t.deletions=[e],t.flags|=16):m.push(e)),t.child=n,t.memoizedState=null,n)}function hc(e,t){return t=li({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function li(e,t){return e=vt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function bc(e,t,n){return Ul(t,e.child,null,n),e=hc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function zd(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),ju(e.return,t,n)}function vc(e,t,n,a,i){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:i}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=a,c.tail=n,c.tailMode=i)}function Bd(e,t,n){var a=t.pendingProps,i=a.revealOrder,c=a.tail;if(ke(e,t,a.children,n),a=Qe.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&zd(e,n,t);else if(e.tag===19)zd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(Z(Qe,a),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Fr(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),vc(t,!1,i,n,c);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Fr(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}vc(t,!0,n,null,c);break;case"together":vc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function sn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Mn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(xa(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=nn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=nn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function yc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Gr(e)))}function H0(e,t,n){switch(t.tag){case 3:Ce(t,t.stateNode.containerInfo),yn(t,qe,e.memoizedState.cache),va();break;case 27:case 5:Vn(t);break;case 4:Ce(t,t.stateNode.containerInfo);break;case 10:yn(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(An(t),t.flags|=128,null):(n&t.child.childLanes)!==0?wd(e,t,n):(An(t),e=sn(e,t,n),e!==null?e.sibling:null);An(t);break;case 19:var i=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(xa(e,t,n,!1),a=(n&t.childLanes)!==0),i){if(a)return Bd(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Z(Qe,Qe.current),a)break;return null;case 22:case 23:return t.lanes=0,Od(e,t,n);case 24:yn(t,qe,e.memoizedState.cache)}return sn(e,t,n)}function jd(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Xe=!0;else{if(!yc(e,n)&&(t.flags&128)===0)return Xe=!1,H0(e,t,n);Xe=(e.flags&131072)!==0}else Xe=!1,be&&(t.flags&1048576)!==0&&ff(t,Ur,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,i=a._init;if(a=i(a._payload),t.type=a,typeof a=="function")Ru(a)?(e=al(a,e),t.tag=1,t=Md(null,t,a,e,n)):(t.tag=0,t=dc(null,t,a,e,n));else{if(a!=null){if(i=a.$$typeof,i===q){t.tag=11,t=Ad(null,t,a,e,n);break e}else if(i===re){t.tag=14,t=Nd(null,t,a,e,n);break e}}throw t=Lt(a)||a,Error(o(306,t,""))}}return t;case 0:return dc(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,i=al(a,t.pendingProps),Md(e,t,a,i,n);case 3:e:{if(Ce(t,t.stateNode.containerInfo),e===null)throw Error(o(387));a=t.pendingProps;var c=t.memoizedState;i=c.element,Qu(e,t),Oa(t,a,null,n);var m=t.memoizedState;if(a=m.cache,yn(t,qe,a),a!==c.cache&&Uu(t,[qe],n,!0),Ta(),a=m.element,c.isDehydrated)if(c={element:a,isDehydrated:!1,cache:m.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=_d(e,t,a,n);break e}else if(a!==i){i=Ct(Error(o(424)),t),ya(i),t=_d(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Be=Qt(e.firstChild),rt=t,be=!0,Pn=null,Zt=!0,n=md(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(va(),a===i){t=sn(e,t,n);break e}ke(e,t,a,n)}t=t.child}return t;case 26:return ni(e,t),e===null?(n=Lm(t.type,null,t.pendingProps,null))?t.memoizedState=n:be||(n=t.type,e=t.pendingProps,a=bi(le.current).createElement(n),a[nt]=t,a[ot]=e,Pe(a,n,e),Ve(a),t.stateNode=a):t.memoizedState=Lm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Vn(t),e===null&&be&&(a=t.stateNode=Um(t.type,t.pendingProps,le.current),rt=t,Zt=!0,i=Be,Bn(t.type)?(Fc=i,Be=Qt(a.firstChild)):Be=i),ke(e,t,t.pendingProps.children,n),ni(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&be&&((i=a=Be)&&(a=db(a,t.type,t.pendingProps,Zt),a!==null?(t.stateNode=a,rt=t,Be=Qt(a.firstChild),Zt=!1,i=!0):i=!1),i||Fn(t)),Vn(t),i=t.type,c=t.pendingProps,m=e!==null?e.memoizedProps:null,a=c.children,$c(i,c)?a=null:m!==null&&$c(i,m)&&(t.flags|=32),t.memoizedState!==null&&(i=Ju(e,t,M0,null,null,n),$a._currentValue=i),ni(e,t),ke(e,t,a,n),t.child;case 6:return e===null&&be&&((e=n=Be)&&(n=mb(n,t.pendingProps,Zt),n!==null?(t.stateNode=n,rt=t,Be=null,e=!0):e=!1),e||Fn(t)),null;case 13:return wd(e,t,n);case 4:return Ce(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Ul(t,null,a,n):ke(e,t,a,n),t.child;case 11:return Ad(e,t,t.type,t.pendingProps,n);case 7:return ke(e,t,t.pendingProps,n),t.child;case 8:return ke(e,t,t.pendingProps.children,n),t.child;case 12:return ke(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,yn(t,t.type,a.value),ke(e,t,a.children,n),t.child;case 9:return i=t.type._context,a=t.pendingProps.children,tl(t),i=lt(i),a=a(i),t.flags|=1,ke(e,t,a,n),t.child;case 14:return Nd(e,t,t.type,t.pendingProps,n);case 15:return Td(e,t,t.type,t.pendingProps,n);case 19:return Bd(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=li(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=nn(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Od(e,t,n);case 24:return tl(t),a=lt(qe),e===null?(i=Lu(),i===null&&(i=Me,c=Gu(),i.pooledCache=c,c.refCount++,c!==null&&(i.pooledCacheLanes|=n),i=c),t.memoizedState={parent:a,cache:i},qu(t),yn(t,qe,i)):((e.lanes&n)!==0&&(Qu(e,t),Oa(t,null,null,n),Ta()),i=e.memoizedState,c=t.memoizedState,i.parent!==a?(i={parent:a,cache:a},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),yn(t,qe,a)):(a=c.cache,yn(t,qe,a),a!==i.cache&&Uu(t,[qe],n,!0))),ke(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function fn(e){e.flags|=4}function Ud(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Xm(t)){if(t=zt.current,t!==null&&((me&4194048)===me?It!==null:(me&62914560)!==me&&(me&536870912)===0||t!==It))throw Aa=Yu,yf;e.flags|=8192}}function ai(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ms():536870912,e.lanes|=t,Yl|=t)}function Ba(e,t){if(!be)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,a|=i.subtreeFlags&65011712,a|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,a|=i.subtreeFlags,a|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function L0(e,t,n){var a=t.pendingProps;switch(wu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return ze(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),un(qe),pt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ba(t)?fn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,pf())),ze(t),null;case 26:return n=t.memoizedState,e===null?(fn(t),n!==null?(ze(t),Ud(t,n)):(ze(t),t.flags&=-16777217)):n?n!==e.memoizedState?(fn(t),ze(t),Ud(t,n)):(ze(t),t.flags&=-16777217):(e.memoizedProps!==a&&fn(t),ze(t),t.flags&=-16777217),null;case 27:na(t),n=le.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&fn(t);else{if(!a){if(t.stateNode===null)throw Error(o(166));return ze(t),null}e=ee.current,ba(t)?df(t):(e=Um(i,a,n),t.stateNode=e,fn(t))}return ze(t),null;case 5:if(na(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&fn(t);else{if(!a){if(t.stateNode===null)throw Error(o(166));return ze(t),null}if(e=ee.current,ba(t))df(t);else{switch(i=bi(le.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?i.createElement("select",{is:a.is}):i.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?i.createElement(n,{is:a.is}):i.createElement(n)}}e[nt]=t,e[ot]=a;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(Pe(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&fn(t)}}return ze(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&fn(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(o(166));if(e=le.current,ba(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,i=rt,i!==null)switch(i.tag){case 27:case 5:a=i.memoizedProps}e[nt]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||Cm(e.nodeValue,n)),e||Fn(t)}else e=bi(e).createTextNode(a),e[nt]=t,t.stateNode=e}return ze(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=ba(t),a!==null&&a.dehydrated!==null){if(e===null){if(!i)throw Error(o(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(o(317));i[nt]=t}else va(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ze(t),i=!1}else i=pf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(on(t),t):(on(t),null)}if(on(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,i=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(i=a.alternate.memoizedState.cachePool.pool);var c=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(c=a.memoizedState.cachePool.pool),c!==i&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ai(t,t.updateQueue),ze(t),null;case 4:return pt(),e===null&&Xc(t.stateNode.containerInfo),ze(t),null;case 10:return un(t.type),ze(t),null;case 19:if($(Qe),i=t.memoizedState,i===null)return ze(t),null;if(a=(t.flags&128)!==0,c=i.rendering,c===null)if(a)Ba(i,!1);else{if(je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Fr(e),c!==null){for(t.flags|=128,Ba(i,!1),e=c.updateQueue,t.updateQueue=e,ai(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)sf(n,e),n=n.sibling;return Z(Qe,Qe.current&1|2),t.child}e=e.sibling}i.tail!==null&&Xt()>ui&&(t.flags|=128,a=!0,Ba(i,!1),t.lanes=4194304)}else{if(!a)if(e=Fr(c),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,ai(t,e),Ba(i,!0),i.tail===null&&i.tailMode==="hidden"&&!c.alternate&&!be)return ze(t),null}else 2*Xt()-i.renderingStartTime>ui&&n!==536870912&&(t.flags|=128,a=!0,Ba(i,!1),t.lanes=4194304);i.isBackwards?(c.sibling=t.child,t.child=c):(e=i.last,e!==null?e.sibling=c:t.child=c,i.last=c)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Xt(),t.sibling=null,e=Qe.current,Z(Qe,a?e&1|2:e&1),t):(ze(t),null);case 22:case 23:return on(t),Iu(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(n&536870912)!==0&&(t.flags&128)===0&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),n=t.updateQueue,n!==null&&ai(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&$(nl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),un(qe),ze(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function Y0(e,t){switch(wu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return un(qe),pt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return na(t),null;case 13:if(on(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));va()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(Qe),null;case 4:return pt(),null;case 10:return un(t.type),null;case 22:case 23:return on(t),Iu(),e!==null&&$(nl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return un(qe),null;case 25:return null;default:return null}}function Gd(e,t){switch(wu(t),t.tag){case 3:un(qe),pt();break;case 26:case 27:case 5:na(t);break;case 4:pt();break;case 13:on(t);break;case 19:$(Qe);break;case 10:un(t.type);break;case 22:case 23:on(t),Iu(),e!==null&&$(nl);break;case 24:un(qe)}}function ja(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var i=a.next;n=i;do{if((n.tag&e)===e){a=void 0;var c=n.create,m=n.inst;a=c(),m.destroy=a}n=n.next}while(n!==i)}}catch(b){Re(t,t.return,b)}}function Tn(e,t,n){try{var a=t.updateQueue,i=a!==null?a.lastEffect:null;if(i!==null){var c=i.next;a=c;do{if((a.tag&e)===e){var m=a.inst,b=m.destroy;if(b!==void 0){m.destroy=void 0,i=t;var E=n,C=b;try{C()}catch(j){Re(i,E,j)}}}a=a.next}while(a!==c)}}catch(j){Re(t,t.return,j)}}function Hd(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Nf(t,n)}catch(a){Re(e,e.return,a)}}}function Ld(e,t,n){n.props=al(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){Re(e,t,a)}}function Ua(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(i){Re(e,t,i)}}function Kt(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(i){Re(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){Re(e,t,i)}else n.current=null}function Yd(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(i){Re(e,e.return,i)}}function xc(e,t,n){try{var a=e.stateNode;ub(a,e.type,n,t),a[ot]=t}catch(i){Re(e,e.return,i)}}function qd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Bn(e.type)||e.tag===4}function Sc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||qd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Bn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ec(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=hi));else if(a!==4&&(a===27&&Bn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Ec(e,t,n),e=e.sibling;e!==null;)Ec(e,t,n),e=e.sibling}function ri(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&Bn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(ri(e,t,n),e=e.sibling;e!==null;)ri(e,t,n),e=e.sibling}function Qd(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);Pe(t,a,n),t[nt]=e,t[ot]=n}catch(c){Re(e,e.return,c)}}var dn=!1,Ge=!1,Dc=!1,Vd=typeof WeakSet=="function"?WeakSet:Set,Ze=null;function q0(e,t){if(e=e.containerInfo,Kc=Di,e=Fs(e),Su(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var i=a.anchorOffset,c=a.focusNode;a=a.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var m=0,b=-1,E=-1,C=0,j=0,G=e,_=null;t:for(;;){for(var w;G!==n||i!==0&&G.nodeType!==3||(b=m+i),G!==c||a!==0&&G.nodeType!==3||(E=m+a),G.nodeType===3&&(m+=G.nodeValue.length),(w=G.firstChild)!==null;)_=G,G=w;for(;;){if(G===e)break t;if(_===n&&++C===i&&(b=m),_===c&&++j===a&&(E=m),(w=G.nextSibling)!==null)break;G=_,_=G.parentNode}G=w}n=b===-1||E===-1?null:{start:b,end:E}}else n=null}n=n||{start:0,end:0}}else n=null;for(Jc={focusedElem:e,selectionRange:n},Di=!1,Ze=t;Ze!==null;)if(t=Ze,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ze=e;else for(;Ze!==null;){switch(t=Ze,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,i=c.memoizedProps,c=c.memoizedState,a=n.stateNode;try{var ae=al(n.type,i,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(ae,c),a.__reactInternalSnapshotBeforeUpdate=e}catch(te){Re(n,n.return,te)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Wc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Wc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,Ze=e;break}Ze=t.return}}function Xd(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:On(e,n),a&4&&ja(5,n);break;case 1:if(On(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(m){Re(n,n.return,m)}else{var i=al(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(m){Re(n,n.return,m)}}a&64&&Hd(n),a&512&&Ua(n,n.return);break;case 3:if(On(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Nf(e,t)}catch(m){Re(n,n.return,m)}}break;case 27:t===null&&a&4&&Qd(n);case 26:case 5:On(e,n),t===null&&a&4&&Yd(n),a&512&&Ua(n,n.return);break;case 12:On(e,n);break;case 13:On(e,n),a&4&&Kd(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=k0.bind(null,n),pb(e,n))));break;case 22:if(a=n.memoizedState!==null||dn,!a){t=t!==null&&t.memoizedState!==null||Ge,i=dn;var c=Ge;dn=a,(Ge=t)&&!c?Rn(e,n,(n.subtreeFlags&8772)!==0):On(e,n),dn=i,Ge=c}break;case 30:break;default:On(e,n)}}function Zd(e){var t=e.alternate;t!==null&&(e.alternate=null,Zd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&nu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var _e=null,dt=!1;function mn(e,t,n){for(n=n.child;n!==null;)Id(e,t,n),n=n.sibling}function Id(e,t,n){if(gt&&typeof gt.onCommitFiberUnmount=="function")try{gt.onCommitFiberUnmount(la,n)}catch{}switch(n.tag){case 26:Ge||Kt(n,t),mn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ge||Kt(n,t);var a=_e,i=dt;Bn(n.type)&&(_e=n.stateNode,dt=!1),mn(e,t,n),Za(n.stateNode),_e=a,dt=i;break;case 5:Ge||Kt(n,t);case 6:if(a=_e,i=dt,_e=null,mn(e,t,n),_e=a,dt=i,_e!==null)if(dt)try{(_e.nodeType===9?_e.body:_e.nodeName==="HTML"?_e.ownerDocument.body:_e).removeChild(n.stateNode)}catch(c){Re(n,t,c)}else try{_e.removeChild(n.stateNode)}catch(c){Re(n,t,c)}break;case 18:_e!==null&&(dt?(e=_e,Bm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Fa(e)):Bm(_e,n.stateNode));break;case 4:a=_e,i=dt,_e=n.stateNode.containerInfo,dt=!0,mn(e,t,n),_e=a,dt=i;break;case 0:case 11:case 14:case 15:Ge||Tn(2,n,t),Ge||Tn(4,n,t),mn(e,t,n);break;case 1:Ge||(Kt(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&Ld(n,t,a)),mn(e,t,n);break;case 21:mn(e,t,n);break;case 22:Ge=(a=Ge)||n.memoizedState!==null,mn(e,t,n),Ge=a;break;default:mn(e,t,n)}}function Kd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Fa(e)}catch(n){Re(t,t.return,n)}}function Q0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Vd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Vd),t;default:throw Error(o(435,e.tag))}}function Ac(e,t){var n=Q0(e);t.forEach(function(a){var i=W0.bind(null,e,a);n.has(a)||(n.add(a),a.then(i,i))})}function yt(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var i=n[a],c=e,m=t,b=m;e:for(;b!==null;){switch(b.tag){case 27:if(Bn(b.type)){_e=b.stateNode,dt=!1;break e}break;case 5:_e=b.stateNode,dt=!1;break e;case 3:case 4:_e=b.stateNode.containerInfo,dt=!0;break e}b=b.return}if(_e===null)throw Error(o(160));Id(c,m,i),_e=null,dt=!1,c=i.alternate,c!==null&&(c.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Jd(t,e),t=t.sibling}var qt=null;function Jd(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:yt(t,e),xt(e),a&4&&(Tn(3,e,e.return),ja(3,e),Tn(5,e,e.return));break;case 1:yt(t,e),xt(e),a&512&&(Ge||n===null||Kt(n,n.return)),a&64&&dn&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var i=qt;if(yt(t,e),xt(e),a&512&&(Ge||n===null||Kt(n,n.return)),a&4){var c=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(a){case"title":c=i.getElementsByTagName("title")[0],(!c||c[ia]||c[nt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=i.createElement(a),i.head.insertBefore(c,i.querySelector("head > title"))),Pe(c,a,n),c[nt]=e,Ve(c),a=c;break e;case"link":var m=Qm("link","href",i).get(a+(n.href||""));if(m){for(var b=0;b<m.length;b++)if(c=m[b],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){m.splice(b,1);break t}}c=i.createElement(a),Pe(c,a,n),i.head.appendChild(c);break;case"meta":if(m=Qm("meta","content",i).get(a+(n.content||""))){for(b=0;b<m.length;b++)if(c=m[b],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){m.splice(b,1);break t}}c=i.createElement(a),Pe(c,a,n),i.head.appendChild(c);break;default:throw Error(o(468,a))}c[nt]=e,Ve(c),a=c}e.stateNode=a}else Vm(i,e.type,e.stateNode);else e.stateNode=qm(i,a,e.memoizedProps);else c!==a?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,a===null?Vm(i,e.type,e.stateNode):qm(i,a,e.memoizedProps)):a===null&&e.stateNode!==null&&xc(e,e.memoizedProps,n.memoizedProps)}break;case 27:yt(t,e),xt(e),a&512&&(Ge||n===null||Kt(n,n.return)),n!==null&&a&4&&xc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(yt(t,e),xt(e),a&512&&(Ge||n===null||Kt(n,n.return)),e.flags&32){i=e.stateNode;try{vl(i,"")}catch(w){Re(e,e.return,w)}}a&4&&e.stateNode!=null&&(i=e.memoizedProps,xc(e,i,n!==null?n.memoizedProps:i)),a&1024&&(Dc=!0);break;case 6:if(yt(t,e),xt(e),a&4){if(e.stateNode===null)throw Error(o(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(w){Re(e,e.return,w)}}break;case 3:if(xi=null,i=qt,qt=vi(t.containerInfo),yt(t,e),qt=i,xt(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{Fa(t.containerInfo)}catch(w){Re(e,e.return,w)}Dc&&(Dc=!1,$d(e));break;case 4:a=qt,qt=vi(e.stateNode.containerInfo),yt(t,e),xt(e),qt=a;break;case 12:yt(t,e),xt(e);break;case 13:yt(t,e),xt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Mc=Xt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Ac(e,a)));break;case 22:i=e.memoizedState!==null;var E=n!==null&&n.memoizedState!==null,C=dn,j=Ge;if(dn=C||i,Ge=j||E,yt(t,e),Ge=j,dn=C,xt(e),a&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||E||dn||Ge||rl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){E=n=t;try{if(c=E.stateNode,i)m=c.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{b=E.stateNode;var G=E.memoizedProps.style,_=G!=null&&G.hasOwnProperty("display")?G.display:null;b.style.display=_==null||typeof _=="boolean"?"":(""+_).trim()}}catch(w){Re(E,E.return,w)}}}else if(t.tag===6){if(n===null){E=t;try{E.stateNode.nodeValue=i?"":E.memoizedProps}catch(w){Re(E,E.return,w)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,Ac(e,n))));break;case 19:yt(t,e),xt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Ac(e,a)));break;case 30:break;case 21:break;default:yt(t,e),xt(e)}}function xt(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(qd(a)){n=a;break}a=a.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var i=n.stateNode,c=Sc(e);ri(e,c,i);break;case 5:var m=n.stateNode;n.flags&32&&(vl(m,""),n.flags&=-33);var b=Sc(e);ri(e,b,m);break;case 3:case 4:var E=n.stateNode.containerInfo,C=Sc(e);Ec(e,C,E);break;default:throw Error(o(161))}}catch(j){Re(e,e.return,j)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function $d(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;$d(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function On(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Xd(e,t.alternate,t),t=t.sibling}function rl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Tn(4,t,t.return),rl(t);break;case 1:Kt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Ld(t,t.return,n),rl(t);break;case 27:Za(t.stateNode);case 26:case 5:Kt(t,t.return),rl(t);break;case 22:t.memoizedState===null&&rl(t);break;case 30:rl(t);break;default:rl(t)}e=e.sibling}}function Rn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,i=e,c=t,m=c.flags;switch(c.tag){case 0:case 11:case 15:Rn(i,c,n),ja(4,c);break;case 1:if(Rn(i,c,n),a=c,i=a.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(C){Re(a,a.return,C)}if(a=c,i=a.updateQueue,i!==null){var b=a.stateNode;try{var E=i.shared.hiddenCallbacks;if(E!==null)for(i.shared.hiddenCallbacks=null,i=0;i<E.length;i++)Af(E[i],b)}catch(C){Re(a,a.return,C)}}n&&m&64&&Hd(c),Ua(c,c.return);break;case 27:Qd(c);case 26:case 5:Rn(i,c,n),n&&a===null&&m&4&&Yd(c),Ua(c,c.return);break;case 12:Rn(i,c,n);break;case 13:Rn(i,c,n),n&&m&4&&Kd(i,c);break;case 22:c.memoizedState===null&&Rn(i,c,n),Ua(c,c.return);break;case 30:break;default:Rn(i,c,n)}t=t.sibling}}function Nc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Sa(n))}function Tc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Sa(e))}function Jt(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)kd(e,t,n,a),t=t.sibling}function kd(e,t,n,a){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Jt(e,t,n,a),i&2048&&ja(9,t);break;case 1:Jt(e,t,n,a);break;case 3:Jt(e,t,n,a),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Sa(e)));break;case 12:if(i&2048){Jt(e,t,n,a),e=t.stateNode;try{var c=t.memoizedProps,m=c.id,b=c.onPostCommit;typeof b=="function"&&b(m,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(E){Re(t,t.return,E)}}else Jt(e,t,n,a);break;case 13:Jt(e,t,n,a);break;case 23:break;case 22:c=t.stateNode,m=t.alternate,t.memoizedState!==null?c._visibility&2?Jt(e,t,n,a):Ga(e,t):c._visibility&2?Jt(e,t,n,a):(c._visibility|=2,Gl(e,t,n,a,(t.subtreeFlags&10256)!==0)),i&2048&&Nc(m,t);break;case 24:Jt(e,t,n,a),i&2048&&Tc(t.alternate,t);break;default:Jt(e,t,n,a)}}function Gl(e,t,n,a,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,m=t,b=n,E=a,C=m.flags;switch(m.tag){case 0:case 11:case 15:Gl(c,m,b,E,i),ja(8,m);break;case 23:break;case 22:var j=m.stateNode;m.memoizedState!==null?j._visibility&2?Gl(c,m,b,E,i):Ga(c,m):(j._visibility|=2,Gl(c,m,b,E,i)),i&&C&2048&&Nc(m.alternate,m);break;case 24:Gl(c,m,b,E,i),i&&C&2048&&Tc(m.alternate,m);break;default:Gl(c,m,b,E,i)}t=t.sibling}}function Ga(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,i=a.flags;switch(a.tag){case 22:Ga(n,a),i&2048&&Nc(a.alternate,a);break;case 24:Ga(n,a),i&2048&&Tc(a.alternate,a);break;default:Ga(n,a)}t=t.sibling}}var Ha=8192;function Hl(e){if(e.subtreeFlags&Ha)for(e=e.child;e!==null;)Wd(e),e=e.sibling}function Wd(e){switch(e.tag){case 26:Hl(e),e.flags&Ha&&e.memoizedState!==null&&Ob(qt,e.memoizedState,e.memoizedProps);break;case 5:Hl(e);break;case 3:case 4:var t=qt;qt=vi(e.stateNode.containerInfo),Hl(e),qt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Ha,Ha=16777216,Hl(e),Ha=t):Hl(e));break;default:Hl(e)}}function Pd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function La(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];Ze=a,em(a,e)}Pd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Fd(e),e=e.sibling}function Fd(e){switch(e.tag){case 0:case 11:case 15:La(e),e.flags&2048&&Tn(9,e,e.return);break;case 3:La(e);break;case 12:La(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ii(e)):La(e);break;default:La(e)}}function ii(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];Ze=a,em(a,e)}Pd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Tn(8,t,t.return),ii(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,ii(t));break;default:ii(t)}e=e.sibling}}function em(e,t){for(;Ze!==null;){var n=Ze;switch(n.tag){case 0:case 11:case 15:Tn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Sa(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,Ze=a;else e:for(n=e;Ze!==null;){a=Ze;var i=a.sibling,c=a.return;if(Zd(a),a===n){Ze=null;break e}if(i!==null){i.return=c,Ze=i;break e}Ze=c}}}var V0={getCacheForType:function(e){var t=lt(qe),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},X0=typeof WeakMap=="function"?WeakMap:Map,Ee=0,Me=null,se=null,me=0,De=0,St=null,Cn=!1,Ll=!1,Oc=!1,pn=0,je=0,Mn=0,il=0,Rc=0,Bt=0,Yl=0,Ya=null,mt=null,Cc=!1,Mc=0,ui=1/0,ci=null,_n=null,We=0,wn=null,ql=null,Ql=0,_c=0,wc=null,tm=null,qa=0,zc=null;function Et(){if((Ee&2)!==0&&me!==0)return me&-me;if(B.T!==null){var e=Cl;return e!==0?e:Yc()}return hs()}function nm(){Bt===0&&(Bt=(me&536870912)===0||be?ds():536870912);var e=zt.current;return e!==null&&(e.flags|=32),Bt}function Dt(e,t,n){(e===Me&&(De===2||De===9)||e.cancelPendingCommit!==null)&&(Vl(e,0),zn(e,me,Bt,!1)),ra(e,n),((Ee&2)===0||e!==Me)&&(e===Me&&((Ee&2)===0&&(il|=n),je===4&&zn(e,me,Bt,!1)),$t(e))}function lm(e,t,n){if((Ee&6)!==0)throw Error(o(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||aa(e,t),i=a?K0(e,t):Uc(e,t,!0),c=a;do{if(i===0){Ll&&!a&&zn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Z0(n)){i=Uc(e,t,!1),c=!1;continue}if(i===2){if(c=t,e.errorRecoveryDisabledLanes&c)var m=0;else m=e.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){t=m;e:{var b=e;i=Ya;var E=b.current.memoizedState.isDehydrated;if(E&&(Vl(b,m).flags|=256),m=Uc(b,m,!1),m!==2){if(Oc&&!E){b.errorRecoveryDisabledLanes|=c,il|=c,i=4;break e}c=mt,mt=i,c!==null&&(mt===null?mt=c:mt.push.apply(mt,c))}i=m}if(c=!1,i!==2)continue}}if(i===1){Vl(e,0),zn(e,t,0,!0);break}e:{switch(a=e,c=i,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:zn(a,t,Bt,!Cn);break e;case 2:mt=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(i=Mc+300-Xt(),10<i)){if(zn(a,t,Bt,!Cn),yr(a,0,!0)!==0)break e;a.timeoutHandle=wm(am.bind(null,a,n,mt,ci,Cc,t,Bt,il,Yl,Cn,c,2,-0,0),i);break e}am(a,n,mt,ci,Cc,t,Bt,il,Yl,Cn,c,0,-0,0)}}break}while(!0);$t(e)}function am(e,t,n,a,i,c,m,b,E,C,j,G,_,w){if(e.timeoutHandle=-1,G=t.subtreeFlags,(G&8192||(G&16785408)===16785408)&&(Ja={stylesheets:null,count:0,unsuspend:Tb},Wd(t),G=Rb(),G!==null)){e.cancelPendingCommit=G(fm.bind(null,e,t,c,n,a,i,m,b,E,j,1,_,w)),zn(e,c,m,!C);return}fm(e,t,c,n,a,i,m,b,E)}function Z0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var i=n[a],c=i.getSnapshot;i=i.value;try{if(!bt(c(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zn(e,t,n,a){t&=~Rc,t&=~il,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var i=t;0<i;){var c=31-ht(i),m=1<<c;a[c]=-1,i&=~m}n!==0&&ps(e,n,t)}function oi(){return(Ee&6)===0?(Qa(0),!1):!0}function Bc(){if(se!==null){if(De===0)var e=se.return;else e=se,rn=el=null,Wu(e),jl=null,wa=0,e=se;for(;e!==null;)Gd(e.alternate,e),e=e.return;se=null}}function Vl(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,ob(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Bc(),Me=e,se=n=nn(e.current,null),me=t,De=0,St=null,Cn=!1,Ll=aa(e,t),Oc=!1,Yl=Bt=Rc=il=Mn=je=0,mt=Ya=null,Cc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var i=31-ht(a),c=1<<i;t|=e[i],a&=~c}return pn=t,_r(),n}function rm(e,t){ce=null,B.H=kr,t===Da||t===Yr?(t=Ef(),De=3):t===yf?(t=Ef(),De=4):De=t===Dd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,St=t,se===null&&(je=1,ti(e,Ct(t,e.current)))}function im(){var e=B.H;return B.H=kr,e===null?kr:e}function um(){var e=B.A;return B.A=V0,e}function jc(){je=4,Cn||(me&4194048)!==me&&zt.current!==null||(Ll=!0),(Mn&134217727)===0&&(il&134217727)===0||Me===null||zn(Me,me,Bt,!1)}function Uc(e,t,n){var a=Ee;Ee|=2;var i=im(),c=um();(Me!==e||me!==t)&&(ci=null,Vl(e,t)),t=!1;var m=je;e:do try{if(De!==0&&se!==null){var b=se,E=St;switch(De){case 8:Bc(),m=6;break e;case 3:case 2:case 9:case 6:zt.current===null&&(t=!0);var C=De;if(De=0,St=null,Xl(e,b,E,C),n&&Ll){m=0;break e}break;default:C=De,De=0,St=null,Xl(e,b,E,C)}}I0(),m=je;break}catch(j){rm(e,j)}while(!0);return t&&e.shellSuspendCounter++,rn=el=null,Ee=a,B.H=i,B.A=c,se===null&&(Me=null,me=0,_r()),m}function I0(){for(;se!==null;)cm(se)}function K0(e,t){var n=Ee;Ee|=2;var a=im(),i=um();Me!==e||me!==t?(ci=null,ui=Xt()+500,Vl(e,t)):Ll=aa(e,t);e:do try{if(De!==0&&se!==null){t=se;var c=St;t:switch(De){case 1:De=0,St=null,Xl(e,t,c,1);break;case 2:case 9:if(xf(c)){De=0,St=null,om(t);break}t=function(){De!==2&&De!==9||Me!==e||(De=7),$t(e)},c.then(t,t);break e;case 3:De=7;break e;case 4:De=5;break e;case 7:xf(c)?(De=0,St=null,om(t)):(De=0,St=null,Xl(e,t,c,7));break;case 5:var m=null;switch(se.tag){case 26:m=se.memoizedState;case 5:case 27:var b=se;if(!m||Xm(m)){De=0,St=null;var E=b.sibling;if(E!==null)se=E;else{var C=b.return;C!==null?(se=C,si(C)):se=null}break t}}De=0,St=null,Xl(e,t,c,5);break;case 6:De=0,St=null,Xl(e,t,c,6);break;case 8:Bc(),je=6;break e;default:throw Error(o(462))}}J0();break}catch(j){rm(e,j)}while(!0);return rn=el=null,B.H=a,B.A=i,Ee=n,se!==null?0:(Me=null,me=0,_r(),je)}function J0(){for(;se!==null&&!hh();)cm(se)}function cm(e){var t=jd(e.alternate,e,pn);e.memoizedProps=e.pendingProps,t===null?si(e):se=t}function om(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Cd(n,t,t.pendingProps,t.type,void 0,me);break;case 11:t=Cd(n,t,t.pendingProps,t.type.render,t.ref,me);break;case 5:Wu(t);default:Gd(n,t),t=se=sf(t,pn),t=jd(n,t,pn)}e.memoizedProps=e.pendingProps,t===null?si(e):se=t}function Xl(e,t,n,a){rn=el=null,Wu(t),jl=null,wa=0;var i=t.return;try{if(G0(e,i,t,n,me)){je=1,ti(e,Ct(n,e.current)),se=null;return}}catch(c){if(i!==null)throw se=i,c;je=1,ti(e,Ct(n,e.current)),se=null;return}t.flags&32768?(be||a===1?e=!0:Ll||(me&536870912)!==0?e=!1:(Cn=e=!0,(a===2||a===9||a===3||a===6)&&(a=zt.current,a!==null&&a.tag===13&&(a.flags|=16384))),sm(t,e)):si(t)}function si(e){var t=e;do{if((t.flags&32768)!==0){sm(t,Cn);return}e=t.return;var n=L0(t.alternate,t,pn);if(n!==null){se=n;return}if(t=t.sibling,t!==null){se=t;return}se=t=e}while(t!==null);je===0&&(je=5)}function sm(e,t){do{var n=Y0(e.alternate,e);if(n!==null){n.flags&=32767,se=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){se=e;return}se=e=n}while(e!==null);je=6,se=null}function fm(e,t,n,a,i,c,m,b,E){e.cancelPendingCommit=null;do fi();while(We!==0);if((Ee&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=Tu,Th(e,n,c,m,b,E),e===Me&&(se=Me=null,me=0),ql=t,wn=e,Ql=n,_c=c,wc=i,tm=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,P0(hr,function(){return hm(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=B.T,B.T=null,i=I.p,I.p=2,m=Ee,Ee|=4;try{q0(e,t,n)}finally{Ee=m,I.p=i,B.T=a}}We=1,dm(),mm(),pm()}}function dm(){if(We===1){We=0;var e=wn,t=ql,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=B.T,B.T=null;var a=I.p;I.p=2;var i=Ee;Ee|=4;try{Jd(t,e);var c=Jc,m=Fs(e.containerInfo),b=c.focusedElem,E=c.selectionRange;if(m!==b&&b&&b.ownerDocument&&Ps(b.ownerDocument.documentElement,b)){if(E!==null&&Su(b)){var C=E.start,j=E.end;if(j===void 0&&(j=C),"selectionStart"in b)b.selectionStart=C,b.selectionEnd=Math.min(j,b.value.length);else{var G=b.ownerDocument||document,_=G&&G.defaultView||window;if(_.getSelection){var w=_.getSelection(),ae=b.textContent.length,te=Math.min(E.start,ae),Te=E.end===void 0?te:Math.min(E.end,ae);!w.extend&&te>Te&&(m=Te,Te=te,te=m);var T=Ws(b,te),N=Ws(b,Te);if(T&&N&&(w.rangeCount!==1||w.anchorNode!==T.node||w.anchorOffset!==T.offset||w.focusNode!==N.node||w.focusOffset!==N.offset)){var R=G.createRange();R.setStart(T.node,T.offset),w.removeAllRanges(),te>Te?(w.addRange(R),w.extend(N.node,N.offset)):(R.setEnd(N.node,N.offset),w.addRange(R))}}}}for(G=[],w=b;w=w.parentNode;)w.nodeType===1&&G.push({element:w,left:w.scrollLeft,top:w.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<G.length;b++){var U=G[b];U.element.scrollLeft=U.left,U.element.scrollTop=U.top}}Di=!!Kc,Jc=Kc=null}finally{Ee=i,I.p=a,B.T=n}}e.current=t,We=2}}function mm(){if(We===2){We=0;var e=wn,t=ql,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=B.T,B.T=null;var a=I.p;I.p=2;var i=Ee;Ee|=4;try{Xd(e,t.alternate,t)}finally{Ee=i,I.p=a,B.T=n}}We=3}}function pm(){if(We===4||We===3){We=0,bh();var e=wn,t=ql,n=Ql,a=tm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?We=5:(We=0,ql=wn=null,gm(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(_n=null),eu(n),t=t.stateNode,gt&&typeof gt.onCommitFiberRoot=="function")try{gt.onCommitFiberRoot(la,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=B.T,i=I.p,I.p=2,B.T=null;try{for(var c=e.onRecoverableError,m=0;m<a.length;m++){var b=a[m];c(b.value,{componentStack:b.stack})}}finally{B.T=t,I.p=i}}(Ql&3)!==0&&fi(),$t(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===zc?qa++:(qa=0,zc=e):qa=0,Qa(0)}}function gm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Sa(t)))}function fi(e){return dm(),mm(),pm(),hm()}function hm(){if(We!==5)return!1;var e=wn,t=_c;_c=0;var n=eu(Ql),a=B.T,i=I.p;try{I.p=32>n?32:n,B.T=null,n=wc,wc=null;var c=wn,m=Ql;if(We=0,ql=wn=null,Ql=0,(Ee&6)!==0)throw Error(o(331));var b=Ee;if(Ee|=4,Fd(c.current),kd(c,c.current,m,n),Ee=b,Qa(0,!1),gt&&typeof gt.onPostCommitFiberRoot=="function")try{gt.onPostCommitFiberRoot(la,c)}catch{}return!0}finally{I.p=i,B.T=a,gm(e,t)}}function bm(e,t,n){t=Ct(n,t),t=fc(e.stateNode,t,2),e=En(e,t,2),e!==null&&(ra(e,2),$t(e))}function Re(e,t,n){if(e.tag===3)bm(e,e,n);else for(;t!==null;){if(t.tag===3){bm(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(_n===null||!_n.has(a))){e=Ct(n,e),n=Sd(2),a=En(t,n,2),a!==null&&(Ed(n,a,t,e),ra(a,2),$t(a));break}}t=t.return}}function Gc(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new X0;var i=new Set;a.set(t,i)}else i=a.get(t),i===void 0&&(i=new Set,a.set(t,i));i.has(n)||(Oc=!0,i.add(n),e=$0.bind(null,e,t,n),t.then(e,e))}function $0(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Me===e&&(me&n)===n&&(je===4||je===3&&(me&62914560)===me&&300>Xt()-Mc?(Ee&2)===0&&Vl(e,0):Rc|=n,Yl===me&&(Yl=0)),$t(e)}function vm(e,t){t===0&&(t=ms()),e=Nl(e,t),e!==null&&(ra(e,t),$t(e))}function k0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),vm(e,n)}function W0(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(t),vm(e,n)}function P0(e,t){return Ft(e,t)}var di=null,Zl=null,Hc=!1,mi=!1,Lc=!1,ul=0;function $t(e){e!==Zl&&e.next===null&&(Zl===null?di=Zl=e:Zl=Zl.next=e),mi=!0,Hc||(Hc=!0,eb())}function Qa(e,t){if(!Lc&&mi){Lc=!0;do for(var n=!1,a=di;a!==null;){if(e!==0){var i=a.pendingLanes;if(i===0)var c=0;else{var m=a.suspendedLanes,b=a.pingedLanes;c=(1<<31-ht(42|e)+1)-1,c&=i&~(m&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,Em(a,c))}else c=me,c=yr(a,a===Me?c:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(c&3)===0||aa(a,c)||(n=!0,Em(a,c));a=a.next}while(n);Lc=!1}}function F0(){ym()}function ym(){mi=Hc=!1;var e=0;ul!==0&&(cb()&&(e=ul),ul=0);for(var t=Xt(),n=null,a=di;a!==null;){var i=a.next,c=xm(a,t);c===0?(a.next=null,n===null?di=i:n.next=i,i===null&&(Zl=n)):(n=a,(e!==0||(c&3)!==0)&&(mi=!0)),a=i}Qa(e)}function xm(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,i=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var m=31-ht(c),b=1<<m,E=i[m];E===-1?((b&n)===0||(b&a)!==0)&&(i[m]=Nh(b,t)):E<=t&&(e.expiredLanes|=b),c&=~b}if(t=Me,n=me,n=yr(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(De===2||De===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Wi(a),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||aa(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&Wi(a),eu(n)){case 2:case 8:n=ss;break;case 32:n=hr;break;case 268435456:n=fs;break;default:n=hr}return a=Sm.bind(null,e),n=Ft(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&Wi(a),e.callbackPriority=2,e.callbackNode=null,2}function Sm(e,t){if(We!==0&&We!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(fi()&&e.callbackNode!==n)return null;var a=me;return a=yr(e,e===Me?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(lm(e,a,t),xm(e,Xt()),e.callbackNode!=null&&e.callbackNode===n?Sm.bind(null,e):null)}function Em(e,t){if(fi())return null;lm(e,t,!0)}function eb(){sb(function(){(Ee&6)!==0?Ft(os,F0):ym()})}function Yc(){return ul===0&&(ul=ds()),ul}function Dm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ar(""+e)}function Am(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function tb(e,t,n,a,i){if(t==="submit"&&n&&n.stateNode===i){var c=Dm((i[ot]||null).action),m=a.submitter;m&&(t=(t=m[ot]||null)?Dm(t.formAction):m.getAttribute("formAction"),t!==null&&(c=t,m=null));var b=new Rr("action","action",null,a,i);e.push({event:b,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ul!==0){var E=m?Am(i,m):new FormData(i);ic(n,{pending:!0,data:E,method:i.method,action:c},null,E)}}else typeof c=="function"&&(b.preventDefault(),E=m?Am(i,m):new FormData(i),ic(n,{pending:!0,data:E,method:i.method,action:c},c,E))},currentTarget:i}]})}}for(var qc=0;qc<Nu.length;qc++){var Qc=Nu[qc],nb=Qc.toLowerCase(),lb=Qc[0].toUpperCase()+Qc.slice(1);Yt(nb,"on"+lb)}Yt(nf,"onAnimationEnd"),Yt(lf,"onAnimationIteration"),Yt(af,"onAnimationStart"),Yt("dblclick","onDoubleClick"),Yt("focusin","onFocus"),Yt("focusout","onBlur"),Yt(x0,"onTransitionRun"),Yt(S0,"onTransitionStart"),Yt(E0,"onTransitionCancel"),Yt(rf,"onTransitionEnd"),gl("onMouseEnter",["mouseout","mouseover"]),gl("onMouseLeave",["mouseout","mouseover"]),gl("onPointerEnter",["pointerout","pointerover"]),gl("onPointerLeave",["pointerout","pointerover"]),Zn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Zn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Zn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Zn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Zn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Zn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Va="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ab=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Va));function Nm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],i=a.event;a=a.listeners;e:{var c=void 0;if(t)for(var m=a.length-1;0<=m;m--){var b=a[m],E=b.instance,C=b.currentTarget;if(b=b.listener,E!==c&&i.isPropagationStopped())break e;c=b,i.currentTarget=C;try{c(i)}catch(j){ei(j)}i.currentTarget=null,c=E}else for(m=0;m<a.length;m++){if(b=a[m],E=b.instance,C=b.currentTarget,b=b.listener,E!==c&&i.isPropagationStopped())break e;c=b,i.currentTarget=C;try{c(i)}catch(j){ei(j)}i.currentTarget=null,c=E}}}}function fe(e,t){var n=t[tu];n===void 0&&(n=t[tu]=new Set);var a=e+"__bubble";n.has(a)||(Tm(t,e,2,!1),n.add(a))}function Vc(e,t,n){var a=0;t&&(a|=4),Tm(n,e,a,t)}var pi="_reactListening"+Math.random().toString(36).slice(2);function Xc(e){if(!e[pi]){e[pi]=!0,vs.forEach(function(n){n!=="selectionchange"&&(ab.has(n)||Vc(n,!1,e),Vc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[pi]||(t[pi]=!0,Vc("selectionchange",!1,t))}}function Tm(e,t,n,a){switch(km(t)){case 2:var i=_b;break;case 8:i=wb;break;default:i=ao}n=i.bind(null,t,n,e),i=void 0,!du||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),a?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Zc(e,t,n,a,i){var c=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var m=a.tag;if(m===3||m===4){var b=a.stateNode.containerInfo;if(b===i)break;if(m===4)for(m=a.return;m!==null;){var E=m.tag;if((E===3||E===4)&&m.stateNode.containerInfo===i)return;m=m.return}for(;b!==null;){if(m=dl(b),m===null)return;if(E=m.tag,E===5||E===6||E===26||E===27){a=c=m;continue e}b=b.parentNode}}a=a.return}ws(function(){var C=c,j=su(n),G=[];e:{var _=uf.get(e);if(_!==void 0){var w=Rr,ae=e;switch(e){case"keypress":if(Tr(n)===0)break e;case"keydown":case"keyup":w=Ph;break;case"focusin":ae="focus",w=hu;break;case"focusout":ae="blur",w=hu;break;case"beforeblur":case"afterblur":w=hu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=js;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Yh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=t0;break;case nf:case lf:case af:w=Vh;break;case rf:w=l0;break;case"scroll":case"scrollend":w=Hh;break;case"wheel":w=r0;break;case"copy":case"cut":case"paste":w=Zh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Gs;break;case"toggle":case"beforetoggle":w=u0}var te=(t&4)!==0,Te=!te&&(e==="scroll"||e==="scrollend"),T=te?_!==null?_+"Capture":null:_;te=[];for(var N=C,R;N!==null;){var U=N;if(R=U.stateNode,U=U.tag,U!==5&&U!==26&&U!==27||R===null||T===null||(U=ca(N,T),U!=null&&te.push(Xa(N,U,R))),Te)break;N=N.return}0<te.length&&(_=new w(_,ae,null,n,j),G.push({event:_,listeners:te}))}}if((t&7)===0){e:{if(_=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",_&&n!==ou&&(ae=n.relatedTarget||n.fromElement)&&(dl(ae)||ae[fl]))break e;if((w||_)&&(_=j.window===j?j:(_=j.ownerDocument)?_.defaultView||_.parentWindow:window,w?(ae=n.relatedTarget||n.toElement,w=C,ae=ae?dl(ae):null,ae!==null&&(Te=f(ae),te=ae.tag,ae!==Te||te!==5&&te!==27&&te!==6)&&(ae=null)):(w=null,ae=C),w!==ae)){if(te=js,U="onMouseLeave",T="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(te=Gs,U="onPointerLeave",T="onPointerEnter",N="pointer"),Te=w==null?_:ua(w),R=ae==null?_:ua(ae),_=new te(U,N+"leave",w,n,j),_.target=Te,_.relatedTarget=R,U=null,dl(j)===C&&(te=new te(T,N+"enter",ae,n,j),te.target=R,te.relatedTarget=Te,U=te),Te=U,w&&ae)t:{for(te=w,T=ae,N=0,R=te;R;R=Il(R))N++;for(R=0,U=T;U;U=Il(U))R++;for(;0<N-R;)te=Il(te),N--;for(;0<R-N;)T=Il(T),R--;for(;N--;){if(te===T||T!==null&&te===T.alternate)break t;te=Il(te),T=Il(T)}te=null}else te=null;w!==null&&Om(G,_,w,te,!1),ae!==null&&Te!==null&&Om(G,Te,ae,te,!0)}}e:{if(_=C?ua(C):window,w=_.nodeName&&_.nodeName.toLowerCase(),w==="select"||w==="input"&&_.type==="file")var k=Zs;else if(Vs(_))if(Is)k=b0;else{k=g0;var oe=p0}else w=_.nodeName,!w||w.toLowerCase()!=="input"||_.type!=="checkbox"&&_.type!=="radio"?C&&cu(C.elementType)&&(k=Zs):k=h0;if(k&&(k=k(e,C))){Xs(G,k,n,j);break e}oe&&oe(e,_,C),e==="focusout"&&C&&_.type==="number"&&C.memoizedProps.value!=null&&uu(_,"number",_.value)}switch(oe=C?ua(C):window,e){case"focusin":(Vs(oe)||oe.contentEditable==="true")&&(El=oe,Eu=C,ha=null);break;case"focusout":ha=Eu=El=null;break;case"mousedown":Du=!0;break;case"contextmenu":case"mouseup":case"dragend":Du=!1,ef(G,n,j);break;case"selectionchange":if(y0)break;case"keydown":case"keyup":ef(G,n,j)}var W;if(vu)e:{switch(e){case"compositionstart":var ne="onCompositionStart";break e;case"compositionend":ne="onCompositionEnd";break e;case"compositionupdate":ne="onCompositionUpdate";break e}ne=void 0}else Sl?qs(e,n)&&(ne="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ne="onCompositionStart");ne&&(Hs&&n.locale!=="ko"&&(Sl||ne!=="onCompositionStart"?ne==="onCompositionEnd"&&Sl&&(W=zs()):(vn=j,mu="value"in vn?vn.value:vn.textContent,Sl=!0)),oe=gi(C,ne),0<oe.length&&(ne=new Us(ne,e,null,n,j),G.push({event:ne,listeners:oe}),W?ne.data=W:(W=Qs(n),W!==null&&(ne.data=W)))),(W=o0?s0(e,n):f0(e,n))&&(ne=gi(C,"onBeforeInput"),0<ne.length&&(oe=new Us("onBeforeInput","beforeinput",null,n,j),G.push({event:oe,listeners:ne}),oe.data=W)),tb(G,e,C,n,j)}Nm(G,t)})}function Xa(e,t,n){return{instance:e,listener:t,currentTarget:n}}function gi(e,t){for(var n=t+"Capture",a=[];e!==null;){var i=e,c=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||c===null||(i=ca(e,n),i!=null&&a.unshift(Xa(e,i,c)),i=ca(e,t),i!=null&&a.push(Xa(e,i,c))),e.tag===3)return a;e=e.return}return[]}function Il(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Om(e,t,n,a,i){for(var c=t._reactName,m=[];n!==null&&n!==a;){var b=n,E=b.alternate,C=b.stateNode;if(b=b.tag,E!==null&&E===a)break;b!==5&&b!==26&&b!==27||C===null||(E=C,i?(C=ca(n,c),C!=null&&m.unshift(Xa(n,C,E))):i||(C=ca(n,c),C!=null&&m.push(Xa(n,C,E)))),n=n.return}m.length!==0&&e.push({event:t,listeners:m})}var rb=/\r\n?/g,ib=/\u0000|\uFFFD/g;function Rm(e){return(typeof e=="string"?e:""+e).replace(rb,`
`).replace(ib,"")}function Cm(e,t){return t=Rm(t),Rm(e)===t}function hi(){}function Ne(e,t,n,a,i,c){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||vl(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&vl(e,""+a);break;case"className":Sr(e,"class",a);break;case"tabIndex":Sr(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Sr(e,n,a);break;case"style":Ms(e,a,c);break;case"data":if(t!=="object"){Sr(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=Ar(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Ne(e,t,"name",i.name,i,null),Ne(e,t,"formEncType",i.formEncType,i,null),Ne(e,t,"formMethod",i.formMethod,i,null),Ne(e,t,"formTarget",i.formTarget,i,null)):(Ne(e,t,"encType",i.encType,i,null),Ne(e,t,"method",i.method,i,null),Ne(e,t,"target",i.target,i,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=Ar(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=hi);break;case"onScroll":a!=null&&fe("scroll",e);break;case"onScrollEnd":a!=null&&fe("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=Ar(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":fe("beforetoggle",e),fe("toggle",e),xr(e,"popover",a);break;case"xlinkActuate":en(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":en(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":en(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":en(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":en(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":en(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":en(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":en(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":en(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":xr(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Uh.get(n)||n,xr(e,n,a))}}function Ic(e,t,n,a,i,c){switch(n){case"style":Ms(e,a,c);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"children":typeof a=="string"?vl(e,a):(typeof a=="number"||typeof a=="bigint")&&vl(e,""+a);break;case"onScroll":a!=null&&fe("scroll",e);break;case"onScrollEnd":a!=null&&fe("scrollend",e);break;case"onClick":a!=null&&(e.onclick=hi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ys.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),c=e[ot]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,i),typeof a=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,i);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):xr(e,n,a)}}}function Pe(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":fe("error",e),fe("load",e);var a=!1,i=!1,c;for(c in n)if(n.hasOwnProperty(c)){var m=n[c];if(m!=null)switch(c){case"src":a=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Ne(e,t,c,m,n,null)}}i&&Ne(e,t,"srcSet",n.srcSet,n,null),a&&Ne(e,t,"src",n.src,n,null);return;case"input":fe("invalid",e);var b=c=m=i=null,E=null,C=null;for(a in n)if(n.hasOwnProperty(a)){var j=n[a];if(j!=null)switch(a){case"name":i=j;break;case"type":m=j;break;case"checked":E=j;break;case"defaultChecked":C=j;break;case"value":c=j;break;case"defaultValue":b=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(o(137,t));break;default:Ne(e,t,a,j,n,null)}}Ts(e,c,b,E,C,m,i,!1),Er(e);return;case"select":fe("invalid",e),a=m=c=null;for(i in n)if(n.hasOwnProperty(i)&&(b=n[i],b!=null))switch(i){case"value":c=b;break;case"defaultValue":m=b;break;case"multiple":a=b;default:Ne(e,t,i,b,n,null)}t=c,n=m,e.multiple=!!a,t!=null?bl(e,!!a,t,!1):n!=null&&bl(e,!!a,n,!0);return;case"textarea":fe("invalid",e),c=i=a=null;for(m in n)if(n.hasOwnProperty(m)&&(b=n[m],b!=null))switch(m){case"value":a=b;break;case"defaultValue":i=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(o(91));break;default:Ne(e,t,m,b,n,null)}Rs(e,a,i,c),Er(e);return;case"option":for(E in n)if(n.hasOwnProperty(E)&&(a=n[E],a!=null))switch(E){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Ne(e,t,E,a,n,null)}return;case"dialog":fe("beforetoggle",e),fe("toggle",e),fe("cancel",e),fe("close",e);break;case"iframe":case"object":fe("load",e);break;case"video":case"audio":for(a=0;a<Va.length;a++)fe(Va[a],e);break;case"image":fe("error",e),fe("load",e);break;case"details":fe("toggle",e);break;case"embed":case"source":case"link":fe("error",e),fe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(C in n)if(n.hasOwnProperty(C)&&(a=n[C],a!=null))switch(C){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Ne(e,t,C,a,n,null)}return;default:if(cu(t)){for(j in n)n.hasOwnProperty(j)&&(a=n[j],a!==void 0&&Ic(e,t,j,a,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(a=n[b],a!=null&&Ne(e,t,b,a,n,null))}function ub(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,c=null,m=null,b=null,E=null,C=null,j=null;for(w in n){var G=n[w];if(n.hasOwnProperty(w)&&G!=null)switch(w){case"checked":break;case"value":break;case"defaultValue":E=G;default:a.hasOwnProperty(w)||Ne(e,t,w,null,a,G)}}for(var _ in a){var w=a[_];if(G=n[_],a.hasOwnProperty(_)&&(w!=null||G!=null))switch(_){case"type":c=w;break;case"name":i=w;break;case"checked":C=w;break;case"defaultChecked":j=w;break;case"value":m=w;break;case"defaultValue":b=w;break;case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(o(137,t));break;default:w!==G&&Ne(e,t,_,w,a,G)}}iu(e,m,b,E,C,j,c,i);return;case"select":w=m=b=_=null;for(c in n)if(E=n[c],n.hasOwnProperty(c)&&E!=null)switch(c){case"value":break;case"multiple":w=E;default:a.hasOwnProperty(c)||Ne(e,t,c,null,a,E)}for(i in a)if(c=a[i],E=n[i],a.hasOwnProperty(i)&&(c!=null||E!=null))switch(i){case"value":_=c;break;case"defaultValue":b=c;break;case"multiple":m=c;default:c!==E&&Ne(e,t,i,c,a,E)}t=b,n=m,a=w,_!=null?bl(e,!!n,_,!1):!!a!=!!n&&(t!=null?bl(e,!!n,t,!0):bl(e,!!n,n?[]:"",!1));return;case"textarea":w=_=null;for(b in n)if(i=n[b],n.hasOwnProperty(b)&&i!=null&&!a.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Ne(e,t,b,null,a,i)}for(m in a)if(i=a[m],c=n[m],a.hasOwnProperty(m)&&(i!=null||c!=null))switch(m){case"value":_=i;break;case"defaultValue":w=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(o(91));break;default:i!==c&&Ne(e,t,m,i,a,c)}Os(e,_,w);return;case"option":for(var ae in n)if(_=n[ae],n.hasOwnProperty(ae)&&_!=null&&!a.hasOwnProperty(ae))switch(ae){case"selected":e.selected=!1;break;default:Ne(e,t,ae,null,a,_)}for(E in a)if(_=a[E],w=n[E],a.hasOwnProperty(E)&&_!==w&&(_!=null||w!=null))switch(E){case"selected":e.selected=_&&typeof _!="function"&&typeof _!="symbol";break;default:Ne(e,t,E,_,a,w)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var te in n)_=n[te],n.hasOwnProperty(te)&&_!=null&&!a.hasOwnProperty(te)&&Ne(e,t,te,null,a,_);for(C in a)if(_=a[C],w=n[C],a.hasOwnProperty(C)&&_!==w&&(_!=null||w!=null))switch(C){case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(o(137,t));break;default:Ne(e,t,C,_,a,w)}return;default:if(cu(t)){for(var Te in n)_=n[Te],n.hasOwnProperty(Te)&&_!==void 0&&!a.hasOwnProperty(Te)&&Ic(e,t,Te,void 0,a,_);for(j in a)_=a[j],w=n[j],!a.hasOwnProperty(j)||_===w||_===void 0&&w===void 0||Ic(e,t,j,_,a,w);return}}for(var T in n)_=n[T],n.hasOwnProperty(T)&&_!=null&&!a.hasOwnProperty(T)&&Ne(e,t,T,null,a,_);for(G in a)_=a[G],w=n[G],!a.hasOwnProperty(G)||_===w||_==null&&w==null||Ne(e,t,G,_,a,w)}var Kc=null,Jc=null;function bi(e){return e.nodeType===9?e:e.ownerDocument}function Mm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function _m(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function $c(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var kc=null;function cb(){var e=window.event;return e&&e.type==="popstate"?e===kc?!1:(kc=e,!0):(kc=null,!1)}var wm=typeof setTimeout=="function"?setTimeout:void 0,ob=typeof clearTimeout=="function"?clearTimeout:void 0,zm=typeof Promise=="function"?Promise:void 0,sb=typeof queueMicrotask=="function"?queueMicrotask:typeof zm<"u"?function(e){return zm.resolve(null).then(e).catch(fb)}:wm;function fb(e){setTimeout(function(){throw e})}function Bn(e){return e==="head"}function Bm(e,t){var n=t,a=0,i=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<a&&8>a){n=a;var m=e.ownerDocument;if(n&1&&Za(m.documentElement),n&2&&Za(m.body),n&4)for(n=m.head,Za(n),m=n.firstChild;m;){var b=m.nextSibling,E=m.nodeName;m[ia]||E==="SCRIPT"||E==="STYLE"||E==="LINK"&&m.rel.toLowerCase()==="stylesheet"||n.removeChild(m),m=b}}if(i===0){e.removeChild(c),Fa(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:a=n.charCodeAt(0)-48;else a=0;n=c}while(n);Fa(t)}function Wc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Wc(n),nu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function db(e,t,n,a){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[ia])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Qt(e.nextSibling),e===null)break}return null}function mb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Qt(e.nextSibling),e===null))return null;return e}function Pc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function pb(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Fc=null;function jm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Um(e,t,n){switch(t=bi(n),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Za(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);nu(e)}var jt=new Map,Gm=new Set;function vi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var gn=I.d;I.d={f:gb,r:hb,D:bb,C:vb,L:yb,m:xb,X:Eb,S:Sb,M:Db};function gb(){var e=gn.f(),t=oi();return e||t}function hb(e){var t=ml(e);t!==null&&t.tag===5&&t.type==="form"?ld(t):gn.r(e)}var Kl=typeof document>"u"?null:document;function Hm(e,t,n){var a=Kl;if(a&&typeof t=="string"&&t){var i=Rt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Gm.has(i)||(Gm.add(i),e={rel:e,crossOrigin:n,href:t},a.querySelector(i)===null&&(t=a.createElement("link"),Pe(t,"link",e),Ve(t),a.head.appendChild(t)))}}function bb(e){gn.D(e),Hm("dns-prefetch",e,null)}function vb(e,t){gn.C(e,t),Hm("preconnect",e,t)}function yb(e,t,n){gn.L(e,t,n);var a=Kl;if(a&&e&&t){var i='link[rel="preload"][as="'+Rt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Rt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Rt(n.imageSizes)+'"]')):i+='[href="'+Rt(e)+'"]';var c=i;switch(t){case"style":c=Jl(e);break;case"script":c=$l(e)}jt.has(c)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),jt.set(c,e),a.querySelector(i)!==null||t==="style"&&a.querySelector(Ia(c))||t==="script"&&a.querySelector(Ka(c))||(t=a.createElement("link"),Pe(t,"link",e),Ve(t),a.head.appendChild(t)))}}function xb(e,t){gn.m(e,t);var n=Kl;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Rt(a)+'"][href="'+Rt(e)+'"]',c=i;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=$l(e)}if(!jt.has(c)&&(e=y({rel:"modulepreload",href:e},t),jt.set(c,e),n.querySelector(i)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ka(c)))return}a=n.createElement("link"),Pe(a,"link",e),Ve(a),n.head.appendChild(a)}}}function Sb(e,t,n){gn.S(e,t,n);var a=Kl;if(a&&e){var i=pl(a).hoistableStyles,c=Jl(e);t=t||"default";var m=i.get(c);if(!m){var b={loading:0,preload:null};if(m=a.querySelector(Ia(c)))b.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=jt.get(c))&&eo(e,n);var E=m=a.createElement("link");Ve(E),Pe(E,"link",e),E._p=new Promise(function(C,j){E.onload=C,E.onerror=j}),E.addEventListener("load",function(){b.loading|=1}),E.addEventListener("error",function(){b.loading|=2}),b.loading|=4,yi(m,t,a)}m={type:"stylesheet",instance:m,count:1,state:b},i.set(c,m)}}}function Eb(e,t){gn.X(e,t);var n=Kl;if(n&&e){var a=pl(n).hoistableScripts,i=$l(e),c=a.get(i);c||(c=n.querySelector(Ka(i)),c||(e=y({src:e,async:!0},t),(t=jt.get(i))&&to(e,t),c=n.createElement("script"),Ve(c),Pe(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(i,c))}}function Db(e,t){gn.M(e,t);var n=Kl;if(n&&e){var a=pl(n).hoistableScripts,i=$l(e),c=a.get(i);c||(c=n.querySelector(Ka(i)),c||(e=y({src:e,async:!0,type:"module"},t),(t=jt.get(i))&&to(e,t),c=n.createElement("script"),Ve(c),Pe(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(i,c))}}function Lm(e,t,n,a){var i=(i=le.current)?vi(i):null;if(!i)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Jl(n.href),n=pl(i).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Jl(n.href);var c=pl(i).hoistableStyles,m=c.get(e);if(m||(i=i.ownerDocument||i,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,m),(c=i.querySelector(Ia(e)))&&!c._p&&(m.instance=c,m.state.loading=5),jt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},jt.set(e,n),c||Ab(i,e,n,m.state))),t&&a===null)throw Error(o(528,""));return m}if(t&&a!==null)throw Error(o(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=$l(n),n=pl(i).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Jl(e){return'href="'+Rt(e)+'"'}function Ia(e){return'link[rel="stylesheet"]['+e+"]"}function Ym(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function Ab(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Pe(t,"link",n),Ve(t),e.head.appendChild(t))}function $l(e){return'[src="'+Rt(e)+'"]'}function Ka(e){return"script[async]"+e}function qm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Rt(n.href)+'"]');if(a)return t.instance=a,Ve(a),a;var i=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ve(a),Pe(a,"style",i),yi(a,n.precedence,e),t.instance=a;case"stylesheet":i=Jl(n.href);var c=e.querySelector(Ia(i));if(c)return t.state.loading|=4,t.instance=c,Ve(c),c;a=Ym(n),(i=jt.get(i))&&eo(a,i),c=(e.ownerDocument||e).createElement("link"),Ve(c);var m=c;return m._p=new Promise(function(b,E){m.onload=b,m.onerror=E}),Pe(c,"link",a),t.state.loading|=4,yi(c,n.precedence,e),t.instance=c;case"script":return c=$l(n.src),(i=e.querySelector(Ka(c)))?(t.instance=i,Ve(i),i):(a=n,(i=jt.get(c))&&(a=y({},n),to(a,i)),e=e.ownerDocument||e,i=e.createElement("script"),Ve(i),Pe(i,"link",a),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,yi(a,n.precedence,e));return t.instance}function yi(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=a.length?a[a.length-1]:null,c=i,m=0;m<a.length;m++){var b=a[m];if(b.dataset.precedence===t)c=b;else if(c!==i)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function eo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function to(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var xi=null;function Qm(e,t,n){if(xi===null){var a=new Map,i=xi=new Map;i.set(n,a)}else i=xi,a=i.get(n),a||(a=new Map,i.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var c=n[i];if(!(c[ia]||c[nt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var m=c.getAttribute(t)||"";m=e+m;var b=a.get(m);b?b.push(c):a.set(m,[c])}}return a}function Vm(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Nb(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Xm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ja=null;function Tb(){}function Ob(e,t,n){if(Ja===null)throw Error(o(475));var a=Ja;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=Jl(n.href),c=e.querySelector(Ia(i));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Si.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=c,Ve(c);return}c=e.ownerDocument||e,n=Ym(n),(i=jt.get(i))&&eo(n,i),c=c.createElement("link"),Ve(c);var m=c;m._p=new Promise(function(b,E){m.onload=b,m.onerror=E}),Pe(c,"link",n),t.instance=c}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Si.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Rb(){if(Ja===null)throw Error(o(475));var e=Ja;return e.stylesheets&&e.count===0&&no(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&no(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Si(){if(this.count--,this.count===0){if(this.stylesheets)no(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ei=null;function no(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ei=new Map,t.forEach(Cb,e),Ei=null,Si.call(e))}function Cb(e,t){if(!(t.state.loading&4)){var n=Ei.get(e);if(n)var a=n.get(null);else{n=new Map,Ei.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<i.length;c++){var m=i[c];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(n.set(m.dataset.precedence,m),a=m)}a&&n.set(null,a)}i=t.instance,m=i.getAttribute("data-precedence"),c=n.get(m)||a,c===a&&n.set(null,i),n.set(m,i),this.count++,a=Si.bind(this),i.addEventListener("load",a),i.addEventListener("error",a),c?c.parentNode.insertBefore(i,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var $a={$$typeof:K,Provider:null,Consumer:null,_currentValue:F,_currentValue2:F,_threadCount:0};function Mb(e,t,n,a,i,c,m,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Pi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pi(0),this.hiddenUpdates=Pi(null),this.identifierPrefix=a,this.onUncaughtError=i,this.onCaughtError=c,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Zm(e,t,n,a,i,c,m,b,E,C,j,G){return e=new Mb(e,t,n,m,b,E,C,G),t=1,c===!0&&(t|=24),c=vt(3,null,null,t),e.current=c,c.stateNode=e,t=Gu(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:a,isDehydrated:n,cache:t},qu(c),e}function Im(e){return e?(e=Tl,e):Tl}function Km(e,t,n,a,i,c){i=Im(i),a.context===null?a.context=i:a.pendingContext=i,a=Sn(t),a.payload={element:n},c=c===void 0?null:c,c!==null&&(a.callback=c),n=En(e,a,t),n!==null&&(Dt(n,e,t),Na(n,e,t))}function Jm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function lo(e,t){Jm(e,t),(e=e.alternate)&&Jm(e,t)}function $m(e){if(e.tag===13){var t=Nl(e,67108864);t!==null&&Dt(t,e,67108864),lo(e,67108864)}}var Di=!0;function _b(e,t,n,a){var i=B.T;B.T=null;var c=I.p;try{I.p=2,ao(e,t,n,a)}finally{I.p=c,B.T=i}}function wb(e,t,n,a){var i=B.T;B.T=null;var c=I.p;try{I.p=8,ao(e,t,n,a)}finally{I.p=c,B.T=i}}function ao(e,t,n,a){if(Di){var i=ro(a);if(i===null)Zc(e,t,a,Ai,n),Wm(e,a);else if(Bb(i,e,t,n,a))a.stopPropagation();else if(Wm(e,a),t&4&&-1<zb.indexOf(e)){for(;i!==null;){var c=ml(i);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var m=Xn(c.pendingLanes);if(m!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;m;){var E=1<<31-ht(m);b.entanglements[1]|=E,m&=~E}$t(c),(Ee&6)===0&&(ui=Xt()+500,Qa(0))}}break;case 13:b=Nl(c,2),b!==null&&Dt(b,c,2),oi(),lo(c,2)}if(c=ro(a),c===null&&Zc(e,t,a,Ai,n),c===i)break;i=c}i!==null&&a.stopPropagation()}else Zc(e,t,a,null,n)}}function ro(e){return e=su(e),io(e)}var Ai=null;function io(e){if(Ai=null,e=dl(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ai=e,null}function km(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(vh()){case os:return 2;case ss:return 8;case hr:case yh:return 32;case fs:return 268435456;default:return 32}default:return 32}}var uo=!1,jn=null,Un=null,Gn=null,ka=new Map,Wa=new Map,Hn=[],zb="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Wm(e,t){switch(e){case"focusin":case"focusout":jn=null;break;case"dragenter":case"dragleave":Un=null;break;case"mouseover":case"mouseout":Gn=null;break;case"pointerover":case"pointerout":ka.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wa.delete(t.pointerId)}}function Pa(e,t,n,a,i,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:c,targetContainers:[i]},t!==null&&(t=ml(t),t!==null&&$m(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Bb(e,t,n,a,i){switch(t){case"focusin":return jn=Pa(jn,e,t,n,a,i),!0;case"dragenter":return Un=Pa(Un,e,t,n,a,i),!0;case"mouseover":return Gn=Pa(Gn,e,t,n,a,i),!0;case"pointerover":var c=i.pointerId;return ka.set(c,Pa(ka.get(c)||null,e,t,n,a,i)),!0;case"gotpointercapture":return c=i.pointerId,Wa.set(c,Pa(Wa.get(c)||null,e,t,n,a,i)),!0}return!1}function Pm(e){var t=dl(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Oh(e.priority,function(){if(n.tag===13){var a=Et();a=Fi(a);var i=Nl(n,a);i!==null&&Dt(i,n,a),lo(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ni(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ro(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);ou=a,n.target.dispatchEvent(a),ou=null}else return t=ml(n),t!==null&&$m(t),e.blockedOn=n,!1;t.shift()}return!0}function Fm(e,t,n){Ni(e)&&n.delete(t)}function jb(){uo=!1,jn!==null&&Ni(jn)&&(jn=null),Un!==null&&Ni(Un)&&(Un=null),Gn!==null&&Ni(Gn)&&(Gn=null),ka.forEach(Fm),Wa.forEach(Fm)}function Ti(e,t){e.blockedOn===t&&(e.blockedOn=null,uo||(uo=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,jb)))}var Oi=null;function ep(e){Oi!==e&&(Oi=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){Oi===e&&(Oi=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],i=e[t+2];if(typeof a!="function"){if(io(a||n)===null)continue;break}var c=ml(n);c!==null&&(e.splice(t,3),t-=3,ic(c,{pending:!0,data:i,method:n.method,action:a},a,i))}}))}function Fa(e){function t(E){return Ti(E,e)}jn!==null&&Ti(jn,e),Un!==null&&Ti(Un,e),Gn!==null&&Ti(Gn,e),ka.forEach(t),Wa.forEach(t);for(var n=0;n<Hn.length;n++){var a=Hn[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Hn.length&&(n=Hn[0],n.blockedOn===null);)Pm(n),n.blockedOn===null&&Hn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var i=n[a],c=n[a+1],m=i[ot]||null;if(typeof c=="function")m||ep(n);else if(m){var b=null;if(c&&c.hasAttribute("formAction")){if(i=c,m=c[ot]||null)b=m.formAction;else if(io(i)!==null)continue}else b=m.action;typeof b=="function"?n[a+1]=b:(n.splice(a,3),a-=3),ep(n)}}}function co(e){this._internalRoot=e}Ri.prototype.render=co.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var n=t.current,a=Et();Km(n,a,e,t,null,null)},Ri.prototype.unmount=co.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Km(e.current,2,null,e,null,null),oi(),t[fl]=null}};function Ri(e){this._internalRoot=e}Ri.prototype.unstable_scheduleHydration=function(e){if(e){var t=hs();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Hn.length&&t!==0&&t<Hn[n].priority;n++);Hn.splice(n,0,e),n===0&&Pm(e)}};var tp=r.version;if(tp!=="19.1.0")throw Error(o(527,tp,"19.1.0"));I.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=g(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var Ub={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ci=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ci.isDisabled&&Ci.supportsFiber)try{la=Ci.inject(Ub),gt=Ci}catch{}}return tr.createRoot=function(e,t){if(!s(e))throw Error(o(299));var n=!1,a="",i=bd,c=vd,m=yd,b=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(m=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=Zm(e,1,!1,null,null,n,a,i,c,m,b,null),e[fl]=t.current,Xc(e),new co(t)},tr.hydrateRoot=function(e,t,n){if(!s(e))throw Error(o(299));var a=!1,i="",c=bd,m=vd,b=yd,E=null,C=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(m=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(E=n.unstable_transitionCallbacks),n.formState!==void 0&&(C=n.formState)),t=Zm(e,1,!0,t,n??null,a,i,c,m,b,E,C),t.context=Im(null),n=t.current,a=Et(),a=Fi(a),i=Sn(a),i.callback=null,En(n,i,a),n=a,t.current.lanes=n,ra(t,n),$t(t),e[fl]=t.current,Xc(e),new Ri(t)},tr.version="19.1.0",tr}var fp;function Zb(){if(fp)return fo.exports;fp=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(r){console.error(r)}}return l(),fo.exports=Xb(),fo.exports}var Ib=Zb();/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kb=l=>l.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Jb=l=>l.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,u,o)=>o?o.toUpperCase():u.toLowerCase()),dp=l=>{const r=Jb(l);return r.charAt(0).toUpperCase()+r.slice(1)},ng=(...l)=>l.filter((r,u,o)=>!!r&&r.trim()!==""&&o.indexOf(r)===u).join(" ").trim(),$b=l=>{for(const r in l)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var kb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wb=V.forwardRef(({color:l="currentColor",size:r=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:s="",children:f,iconNode:d,...h},g)=>V.createElement("svg",{ref:g,...kb,width:r,height:r,stroke:l,strokeWidth:o?Number(u)*24/Number(r):u,className:ng("lucide",s),...!f&&!$b(h)&&{"aria-hidden":"true"},...h},[...d.map(([p,y])=>V.createElement(p,y)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ct=(l,r)=>{const u=V.forwardRef(({className:o,...s},f)=>V.createElement(Wb,{ref:f,iconNode:r,className:ng(`lucide-${Kb(dp(l))}`,`lucide-${l}`,o),...s}));return u.displayName=dp(l),u};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pb=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Fb=ct("arrow-left",Pb);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ev=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],lg=ct("book-open",ev);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tv=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],nv=ct("brain",tv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lv=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],av=ct("calendar",lv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rv=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Wt=ct("check",rv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iv=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],mp=ct("chevron-right",iv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uv=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],cv=ct("circle-check-big",uv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ov=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],sv=ct("circle-x",ov);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],ag=ct("clock",fv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dv=[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]],mv=ct("grip-vertical",dv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pv=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]],gv=ct("link",pv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hv=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],bv=ct("pen-line",hv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],Mo=ct("target",vv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yv=[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]],xv=ct("trophy",yv);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sv=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Pt=ct("x",Sv),Ev={title:"The Weimar Republic, 1918–29",subtopics:[{id:"1.1",title:"Origins of the Republic, 1918–19",content:["End of WWI: Kaiser Wilhelm II abdicated (Nov 1918), armistice signed, Weimar Republic formed.","Weimar Constitution: Democratic system with elected president, Reichstag, proportional representation, Article 48 (emergency powers).","Strengths: Universal suffrage, democratic elections.","Weaknesses: Coalition instability, Article 48 misuse risk."]},{id:"1.2",title:"Early Challenges to the Weimar Republic, 1919–23",content:["Unpopularity: 'Stab in the back' myth blamed republic for WWI defeat; Treaty of Versailles (1919) imposed war guilt, reparations, territorial losses.","Left-wing challenge: Spartacist Uprising (1919, led by Rosa Luxemburg, crushed by Freikorps).","Right-wing challenge: Kapp Putsch (1920, Wolfgang Kapp, failed due to general strike).","1923 Crises: Hyperinflation due to reparations and money printing; French occupation of Ruhr (1923) disrupted economy, passive resistance."]},{id:"1.3",title:"Golden Years: Recovery of the Republic, 1924–29",content:["Economic recovery: Gustav Stresemann introduced Rentenmark (1923) to stabilize currency; Dawes Plan (1924) and Young Plan (1929) restructured reparations; American loans boosted industry.","International acceptance: Locarno Pact (1925) improved relations with France and Britain; Germany joined League of Nations (1926).","Stresemann’s role: Key figure in stabilizing economy and diplomacy."]},{id:"1.4",title:"Changes in Society, 1924–29",content:["Standard of living: Improved wages, housing, and social welfare.","Women: Gained voting rights, increased employment in offices and politics, but traditional roles persisted.","Culture: Bauhaus architecture, expressionist art, thriving cinema (e.g., Metropolis by Fritz Lang)."]}]},Dv={title:"Hitler’s Rise to Power, 1919–33",subtopics:[{id:"2.1",title:"Early Development of the Nazi Party, 1920–22",content:["Hitler’s early career: Joined German Workers’ Party (1919), renamed it Nazi Party, became leader.","Twenty-Five Point Programme: Anti-Semitic, nationalist, anti-Versailles policies.","SA (Stormtroopers): Used for propaganda, intimidation, and rallies."]},{id:"2.2",title:"Munich Putsch and Nazi Party, 1923–28",content:["Munich Putsch (1923): Failed coup attempt; Hitler arrested, wrote Mein Kampf in prison.","Consequences: Nazi Party banned temporarily, Hitler gained publicity.","Lean Years (1924–28): Limited support due to economic recovery; Bamberg Conference (1926) reorganized party under Hitler’s control."]},{id:"2.3",title:"Growth in Support for the Nazis, 1929–32",content:["Wall Street Crash (1929): Caused unemployment, economic collapse; boosted Communist Party, scaring middle classes to Nazis.","Nazi appeal: Hitler’s charisma, anti-communist stance, effective propaganda (posters, rallies), SA’s street presence."]},{id:"2.4",title:"How Hitler Became Chancellor, 1932–33",content:["1932 Elections: Nazis became largest party but no majority; Hindenburg reluctant to appoint Hitler.","Backroom deals: Hindenburg and von Papen appointed Hitler Chancellor (Jan 1933) to control him."]}]},Av={title:"Nazi Control and Dictatorship, 1933–39",subtopics:[{id:"3.1",title:"Creation of a Dictatorship, 1933–34",content:["Reichstag Fire (Feb 1933): Blamed on communists, led to emergency decree suspending freedoms.","Enabling Act (March 1933): Allowed Hitler to pass laws without Reichstag; banned other parties and trade unions.","Night of the Long Knives (June 1934): Eliminated SA leader Ernst Röhm and rivals.","Hindenburg’s death (Aug 1934): Hitler became Führer; army swore oath of allegiance."]},{id:"3.2",title:"The Police State",content:["Gestapo: Secret police monitored and arrested opponents.","SS: Elite force, ran concentration camps for political prisoners.","Legal system: Judges swore loyalty to Hitler; People’s Court for political trials."]},{id:"3.3",title:"Controlling and Influencing Attitudes",content:["Goebbels’ Propaganda Ministry: Censored media, organized Nuremberg Rallies, controlled Berlin Olympics (1936).","Culture: Promoted Nazi-approved art, architecture; banned 'degenerate' art.","Religion: Concordat (1933) with Catholic Church (limited success); Reich Church controlled Protestants."]},{id:"3.4",title:"Opposition, Resistance, and Conformity",content:["Conformity: Widespread due to fear, propaganda, and economic benefits.","Opposition: Pastor Niemöller (Confessing Church), Swing Youth, Edelweiss Pirates; limited impact due to repression."]}]},Nv={title:"Life in Nazi Germany, 1933–39",subtopics:[{id:"4.1",title:"Nazi Policies Towards Women",content:["Ideology: Women as homemakers, focused on marriage and motherhood.","Policies: Mother’s Cross for large families, restricted employment, promoted traditional appearance."]},{id:"4.2",title:"Nazi Policies Towards the Young",content:["Hitler Youth (boys) and League of German Maidens (girls): Indoctrinated loyalty, fitness.","Education: Nazi curriculum, anti-Semitic teachings, loyal teachers."]},{id:"4.3",title:"Employment and Living Standards",content:["Reduced unemployment: Public works (autobahns), rearmament, National Labour Service, 'invisible unemployment' (e.g., excluding Jews, women).","Living standards: Mixed; Strength Through Joy (holidays), Beauty of Labour (workplace improvements), but stagnant wages.","Labour Front: Replaced trade unions, controlled workers."]},{id:"4.4",title:"Persecution of Minorities",content:["Racial ideology: Targeted Slavs, Roma, homosexuals, disabled (e.g., sterilization).","Jewish persecution: 1933 boycott of shops, Nuremberg Laws (1935) stripped citizenship, Kristallnacht (1938) attacked businesses."]}]},Tv={keyTopic1:Ev,keyTopic2:Dv,keyTopic3:Av,keyTopic4:Nv},Ov=Tv,Rv=()=>Object.entries(Ov).map(([l,r])=>({id:l,title:r.title,subtopics:r.subtopics})),Cv=({onStartQuiz:l})=>{const[r,u]=V.useState(null),o=Rv(),s=d=>{u(d)},f=()=>{r&&l(r)};return S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("h2",{className:"text-2xl font-bold text-slate-100 mb-2",children:"Choose Your Topic"}),S.jsx("p",{className:"text-slate-400",children:"Select a topic to start your revision quiz"})]}),S.jsx("div",{className:"grid gap-4",children:o.map(d=>S.jsxs("div",{className:`card cursor-pointer transition-all duration-200 hover:border-blue-500 ${(r==null?void 0:r.id)===d.id?"border-blue-500 bg-blue-500/10":"hover:bg-slate-800/80"}`,onClick:()=>s(d),children:[S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{className:"flex items-center gap-4",children:[S.jsx("div",{className:`p-3 rounded-lg ${(r==null?void 0:r.id)===d.id?"bg-blue-500":"bg-slate-700"}`,children:S.jsx(lg,{className:`w-6 h-6 ${(r==null?void 0:r.id)===d.id?"text-white":"text-slate-400"}`})}),S.jsxs("div",{children:[S.jsx("h3",{className:"text-lg font-semibold text-slate-100",children:d.title}),S.jsxs("p",{className:"text-slate-400 text-sm",children:[d.subtopics.length," subtopics available"]})]})]}),S.jsx(mp,{className:`w-5 h-5 transition-colors ${(r==null?void 0:r.id)===d.id?"text-blue-500":"text-slate-400"}`})]}),(r==null?void 0:r.id)===d.id&&S.jsxs("div",{className:"mt-4 pt-4 border-t border-slate-700",children:[S.jsx("h4",{className:"text-sm font-medium text-slate-100 mb-3",children:"Subtopics:"}),S.jsx("div",{className:"grid gap-2",children:d.subtopics.map(h=>S.jsxs("div",{className:"flex items-center gap-2 text-sm text-slate-400",children:[S.jsx("div",{className:"w-1.5 h-1.5 bg-blue-500 rounded-full"}),S.jsx("span",{children:h.title})]},h.id))})]})]},d.id))}),r&&S.jsx("div",{className:"card bg-blue-500/10 border-blue-500",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("h3",{className:"text-lg font-semibold text-slate-100 mb-2",children:"Ready to Start?"}),S.jsxs("div",{className:"flex items-center gap-6 text-sm text-slate-400",children:[S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx(Mo,{className:"w-4 h-4"}),S.jsx("span",{children:"20 Questions"})]}),S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx(ag,{className:"w-4 h-4"}),S.jsx("span",{children:"~15 minutes"})]})]})]}),S.jsxs("button",{onClick:f,className:"btn-primary flex items-center gap-2",children:["Start Quiz",S.jsx(mp,{className:"w-4 h-4"})]})]})}),S.jsx("div",{className:"card bg-yellow-500/10 border-yellow-500",children:S.jsxs("div",{className:"flex items-center gap-3",children:[S.jsx("div",{className:"p-2 bg-yellow-500 rounded-lg",children:S.jsx(Mo,{className:"w-5 h-5 text-white"})}),S.jsxs("div",{children:[S.jsx("h3",{className:"font-semibold text-slate-100",children:"Mixed Practice"}),S.jsx("p",{className:"text-slate-400 text-sm",children:"Practice with questions from all topics"})]}),S.jsx("button",{onClick:()=>l(null),className:"btn-secondary ml-auto",children:"Start Mixed Quiz"})]})})]})},rg=V.createContext(),_o={currentQuestion:0,score:0,answers:[],isComplete:!1,questions:[],timeSpent:0,startTime:null},Mv=(l,r)=>{switch(r.type){case"START_QUIZ":return{..._o,questions:r.questions,startTime:Date.now()};case"ANSWER_QUESTION":const u=[...l.answers];return u[l.currentQuestion]={answer:r.answer,isCorrect:r.isCorrect,timeSpent:Date.now()-l.startTime},{...l,answers:u,score:r.isCorrect?l.score+1:l.score};case"NEXT_QUESTION":const o=l.currentQuestion+1;return{...l,currentQuestion:o,isComplete:o>=l.questions.length};case"PREVIOUS_QUESTION":return{...l,currentQuestion:Math.max(0,l.currentQuestion-1)};case"COMPLETE_QUIZ":return{...l,isComplete:!0,timeSpent:Date.now()-l.startTime};case"RESET_QUIZ":return _o;default:return l}},_v=({children:l})=>{const[r,u]=V.useReducer(Mv,_o),p={...r,startQuiz:y=>{u({type:"START_QUIZ",questions:y})},answerQuestion:(y,v)=>{u({type:"ANSWER_QUESTION",answer:y,isCorrect:v})},nextQuestion:()=>{u({type:"NEXT_QUESTION"})},previousQuestion:()=>{u({type:"PREVIOUS_QUESTION"})},completeQuiz:()=>{u({type:"COMPLETE_QUIZ"})},resetQuiz:()=>{u({type:"RESET_QUIZ"})}};return S.jsx(rg.Provider,{value:p,children:l})},wv=()=>{const l=V.useContext(rg);if(!l)throw new Error("useQuiz must be used within a QuizProvider");return l},kt={MULTIPLE_CHOICE:"multiple_choice",TRUE_FALSE:"true_false",MATCH_UP:"match_up",DRAG_ORDER:"drag_order",FILL_GAPS:"fill_gaps"},pp=(l,r=20)=>{console.log("generateQuestions called with:",l,r);const u=[{id:"test_1",type:kt.TRUE_FALSE,question:"The Weimar Republic was established in Germany after World War I.",correctAnswer:!0,topic:"The Weimar Republic",subtopic:"Origins"},{id:"test_2",type:kt.TRUE_FALSE,question:"Hitler became Chancellor of Germany in 1933.",correctAnswer:!0,topic:"Hitler's Rise to Power",subtopic:"Political Rise"},{id:"test_3",type:kt.TRUE_FALSE,question:"The Nazi Party was originally called the German Workers' Party.",correctAnswer:!0,topic:"Hitler's Rise to Power",subtopic:"Early Development"},{id:"test_4",type:kt.TRUE_FALSE,question:"The Reichstag Fire occurred in 1932.",correctAnswer:!1,topic:"Nazi Control",subtopic:"Creation of Dictatorship"},{id:"test_5",type:kt.TRUE_FALSE,question:"The Enabling Act allowed Hitler to pass laws without the Reichstag.",correctAnswer:!0,topic:"Nazi Control",subtopic:"Creation of Dictatorship"}];return console.log("Returning test questions:",u.length),u.slice(0,r)},zv=({question:l,onAnswer:r,isAnswered:u,userAnswer:o})=>{const[s,f]=V.useState(o||null),d=p=>{if(u)return;f(p);const y=p===l.correctAnswer;r(p,y)},h=p=>u?p===l.correctAnswer?"border-green-500 bg-green-500/10":p===s&&p!==l.correctAnswer?"border-red-500 bg-red-500/10":"border-slate-700 opacity-50":s===p?"border-blue-500 bg-blue-500/10":"border-slate-700 hover:border-blue-400 hover:bg-blue-400/5",g=p=>u?p===l.correctAnswer?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):p===s&&p!==l.correctAnswer?S.jsx(Pt,{className:"w-5 h-5 text-red-500"}):null:null;return S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm text-blue-500 font-medium mb-2",children:"Multiple Choice"}),S.jsx("h3",{className:"text-xl font-semibold text-slate-100 mb-2",children:l.question}),l.topic&&S.jsxs("div",{className:"text-sm text-slate-400",children:["Topic: ",l.topic," ",l.subtopic&&`• ${l.subtopic}`]})]}),S.jsx("div",{className:"space-y-3",children:l.options.map((p,y)=>S.jsx("button",{onClick:()=>d(p),disabled:u,className:`w-full p-4 rounded-lg border-2 text-left transition-all duration-200 ${h(p)} ${u?"cursor-default":"cursor-pointer"}`,children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{className:"flex items-center gap-3",children:[S.jsx("div",{className:`w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-medium ${s===p&&!u?"border-blue-500 bg-blue-500 text-white":"border-slate-700 text-slate-400"}`,children:String.fromCharCode(65+y)}),S.jsx("span",{className:"text-slate-100",children:p})]}),g(p)]})},y))}),u&&S.jsxs("div",{className:`p-4 rounded-lg border ${s===l.correctAnswer?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}`,children:[S.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s===l.correctAnswer?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):S.jsx(Pt,{className:"w-5 h-5 text-red-500"}),S.jsx("span",{className:`font-medium ${s===l.correctAnswer?"text-green-500":"text-red-500"}`,children:s===l.correctAnswer?"Correct!":"Incorrect"})]}),s!==l.correctAnswer&&S.jsxs("p",{className:"text-slate-400 text-sm",children:["The correct answer is: ",S.jsx("span",{className:"font-medium text-green-500",children:l.correctAnswer})]})]})]})},Bv=({question:l,onAnswer:r,isAnswered:u,userAnswer:o})=>{const[s,f]=V.useState(o!==void 0?o:null),d=p=>{if(u)return;f(p);const y=p===l.correctAnswer;r(p,y)},h=p=>u?p===l.correctAnswer?"border-green-500 bg-green-500/10":p===s&&p!==l.correctAnswer?"border-red-500 bg-red-500/10":"border-slate-700 opacity-50":s===p?"border-blue-500 bg-blue-500/10":"border-slate-700 hover:border-blue-400 hover:bg-blue-400/5",g=p=>u?p===l.correctAnswer?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):p===s&&p!==l.correctAnswer?S.jsx(Pt,{className:"w-5 h-5 text-red-500"}):null:null;return S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm text-purple-500 font-medium mb-2",children:"True or False"}),S.jsx("h3",{className:"text-xl font-semibold text-slate-100 mb-2",children:l.question}),l.topic&&S.jsxs("div",{className:"text-sm text-slate-400",children:["Topic: ",l.topic," ",l.subtopic&&`• ${l.subtopic}`]})]}),S.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[S.jsx("button",{onClick:()=>d(!0),disabled:u,className:`p-6 rounded-lg border-2 transition-all duration-200 ${h(!0)} ${u?"cursor-default":"cursor-pointer"}`,children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{className:"flex items-center gap-3",children:[S.jsx("div",{className:`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${s===!0&&!u?"border-blue-500 bg-blue-500 text-white":"border-slate-700 text-slate-400"}`,children:"T"}),S.jsx("span",{className:"text-lg font-medium text-slate-100",children:"True"})]}),g(!0)]})}),S.jsx("button",{onClick:()=>d(!1),disabled:u,className:`p-6 rounded-lg border-2 transition-all duration-200 ${h(!1)} ${u?"cursor-default":"cursor-pointer"}`,children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{className:"flex items-center gap-3",children:[S.jsx("div",{className:`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${s===!1&&!u?"border-blue-500 bg-blue-500 text-white":"border-slate-700 text-slate-400"}`,children:"F"}),S.jsx("span",{className:"text-lg font-medium text-slate-100",children:"False"})]}),g(!1)]})})]}),u&&S.jsxs("div",{className:`p-4 rounded-lg border ${s===l.correctAnswer?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}`,children:[S.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s===l.correctAnswer?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):S.jsx(Pt,{className:"w-5 h-5 text-red-500"}),S.jsx("span",{className:`font-medium ${s===l.correctAnswer?"text-green-500":"text-red-500"}`,children:s===l.correctAnswer?"Correct!":"Incorrect"})]}),S.jsxs("p",{className:"text-slate-400 text-sm",children:["The correct answer is: ",S.jsx("span",{className:"font-medium text-green-500",children:l.correctAnswer?"True":"False"})]})]})]})},jv=({question:l,onAnswer:r,isAnswered:u,userAnswer:o})=>{const[s,f]=V.useState(o||{}),[d,h]=V.useState(null),g=l.pairs.map(O=>O.left),p=[...l.pairs.map(O=>O.right)].sort(()=>Math.random()-.5),y=O=>{u||h(O)},v=O=>{if(u||!d)return;const Y={...s};if(Object.keys(Y).forEach(Q=>{Y[Q]===O&&delete Y[Q]}),Y[d]=O,f(Y),h(null),Object.keys(Y).length===l.pairs.length){let Q=0;l.pairs.forEach(q=>{Y[q.left]===q.right&&Q++});const K=Q===l.pairs.length;r(Y,K)}},x=O=>{var Y;if(d===O)return"border-blue-500 bg-blue-500/10";if(s[O]){if(u){const Q=(Y=l.pairs.find(K=>K.left===O))==null?void 0:Y.right;return s[O]===Q?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}return"border-yellow-500 bg-yellow-500/10"}return"border-slate-700 hover:border-blue-400"},D=O=>{var Q;const Y=Object.values(s).includes(O);if(Y&&u){const K=Object.keys(s).find(H=>s[H]===O),q=(Q=l.pairs.find(H=>H.left===K))==null?void 0:Q.right;return O===q?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}return Y?"border-yellow-500 bg-yellow-500/10":"border-slate-700 hover:border-blue-400"},M=(O,Y=!0)=>{var Q,K;if(!u)return null;if(Y){const q=(Q=l.pairs.find(H=>H.left===O))==null?void 0:Q.right;return s[O]===q?S.jsx(Wt,{className:"w-4 h-4 text-green-500"}):S.jsx(Pt,{className:"w-4 h-4 text-red-500"})}else{const q=Object.keys(s).find(J=>s[J]===O);if(!q)return null;const H=(K=l.pairs.find(J=>J.left===q))==null?void 0:K.right;return O===H?S.jsx(Wt,{className:"w-4 h-4 text-green-500"}):S.jsx(Pt,{className:"w-4 h-4 text-red-500"})}},z=u?l.pairs.filter(O=>s[O.left]===O.right).length:0;return S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm text-green-500 font-medium mb-2",children:"Match Up"}),S.jsx("h3",{className:"text-xl font-semibold text-slate-100 mb-2",children:l.question}),l.topic&&S.jsxs("div",{className:"text-sm text-slate-400",children:["Topic: ",l.topic]}),S.jsx("p",{className:"text-slate-400 text-sm mt-2",children:"Click an item on the left, then click its match on the right"})]}),S.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[S.jsxs("div",{className:"space-y-3",children:[S.jsx("h4",{className:"font-medium text-slate-100 text-center",children:"Items"}),g.map((O,Y)=>S.jsx("button",{onClick:()=>y(O),disabled:u,className:`w-full p-3 rounded-lg border-2 text-left transition-all duration-200 ${x(O)} ${u?"cursor-default":"cursor-pointer"}`,children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("span",{className:"text-slate-100",children:O}),S.jsxs("div",{className:"flex items-center gap-2",children:[s[O]&&S.jsx(gv,{className:"w-4 h-4 text-yellow-500"}),M(O,!0)]})]})},Y))]}),S.jsxs("div",{className:"space-y-3",children:[S.jsx("h4",{className:"font-medium text-slate-100 text-center",children:"Matches"}),p.map((O,Y)=>S.jsx("button",{onClick:()=>v(O),disabled:u||!d,className:`w-full p-3 rounded-lg border-2 text-left transition-all duration-200 ${D(O)} ${u||!d?"cursor-default":"cursor-pointer"}`,children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("span",{className:"text-slate-100",children:O}),M(O,!1)]})},Y))]})]}),u&&S.jsxs("div",{className:`p-4 rounded-lg border ${z===l.pairs.length?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}`,children:[S.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[z===l.pairs.length?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):S.jsx(Pt,{className:"w-5 h-5 text-red-500"}),S.jsx("span",{className:`font-medium ${z===l.pairs.length?"text-green-500":"text-red-500"}`,children:z===l.pairs.length?"Perfect!":"Some matches incorrect"})]}),S.jsxs("p",{className:"text-slate-400 text-sm",children:["You got ",z," out of ",l.pairs.length," matches correct"]})]})]})};var Yo=tg();const Uv=eg(Yo);function ut(l){return`Minified Redux error #${l}; visit https://redux.js.org/Errors?code=${l} for the full message or use the non-minified dev environment for full errors. `}var Gv=typeof Symbol=="function"&&Symbol.observable||"@@observable",gp=Gv,hp=()=>Math.random().toString(36).substring(7).split("").join("."),Hv={INIT:`@@redux/INIT${hp()}`,REPLACE:`@@redux/REPLACE${hp()}`},bp=Hv;function Lv(l){if(typeof l!="object"||l===null)return!1;let r=l;for(;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(l)===r||Object.getPrototypeOf(l)===null}function ig(l,r,u){if(typeof l!="function")throw new Error(ut(2));if(typeof r=="function"&&typeof u=="function"||typeof u=="function"&&typeof arguments[3]=="function")throw new Error(ut(0));if(typeof r=="function"&&typeof u>"u"&&(u=r,r=void 0),typeof u<"u"){if(typeof u!="function")throw new Error(ut(1));return u(ig)(l,r)}let o=l,s=r,f=new Map,d=f,h=0,g=!1;function p(){d===f&&(d=new Map,f.forEach((O,Y)=>{d.set(Y,O)}))}function y(){if(g)throw new Error(ut(3));return s}function v(O){if(typeof O!="function")throw new Error(ut(4));if(g)throw new Error(ut(5));let Y=!0;p();const Q=h++;return d.set(Q,O),function(){if(Y){if(g)throw new Error(ut(6));Y=!1,p(),d.delete(Q),f=null}}}function x(O){if(!Lv(O))throw new Error(ut(7));if(typeof O.type>"u")throw new Error(ut(8));if(typeof O.type!="string")throw new Error(ut(17));if(g)throw new Error(ut(9));try{g=!0,s=o(s,O)}finally{g=!1}return(f=d).forEach(Q=>{Q()}),O}function D(O){if(typeof O!="function")throw new Error(ut(10));o=O,x({type:bp.REPLACE})}function M(){const O=v;return{subscribe(Y){if(typeof Y!="object"||Y===null)throw new Error(ut(11));function Q(){const q=Y;q.next&&q.next(y())}return Q(),{unsubscribe:O(Q)}},[gp](){return this}}}return x({type:bp.INIT}),{dispatch:x,subscribe:v,getState:y,replaceReducer:D,[gp]:M}}function vp(l,r){return function(...u){return r(l.apply(this,u))}}function yp(l,r){if(typeof l=="function")return vp(l,r);if(typeof l!="object"||l===null)throw new Error(ut(16));const u={};for(const o in l){const s=l[o];typeof s=="function"&&(u[o]=vp(s,r))}return u}function ug(...l){return l.length===0?r=>r:l.length===1?l[0]:l.reduce((r,u)=>(...o)=>r(u(...o)))}function Yv(...l){return r=>(u,o)=>{const s=r(u,o);let f=()=>{throw new Error(ut(15))};const d={getState:s.getState,dispatch:(g,...p)=>f(g,...p)},h=l.map(g=>g(d));return f=ug(...h)(s.dispatch),{...s,dispatch:f}}}var ho={exports:{}},bo={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xp;function qv(){if(xp)return bo;xp=1;var l=Vi();function r(g,p){return g===p&&(g!==0||1/g===1/p)||g!==g&&p!==p}var u=typeof Object.is=="function"?Object.is:r,o=l.useSyncExternalStore,s=l.useRef,f=l.useEffect,d=l.useMemo,h=l.useDebugValue;return bo.useSyncExternalStoreWithSelector=function(g,p,y,v,x){var D=s(null);if(D.current===null){var M={hasValue:!1,value:null};D.current=M}else M=D.current;D=d(function(){function O(H){if(!Y){if(Y=!0,Q=H,H=v(H),x!==void 0&&M.hasValue){var J=M.value;if(x(J,H))return K=J}return K=H}if(J=K,u(Q,H))return J;var re=v(H);return x!==void 0&&x(J,re)?(Q=H,J):(Q=H,K=re)}var Y=!1,Q,K,q=y===void 0?null:y;return[function(){return O(p())},q===null?void 0:function(){return O(q())}]},[p,y,v,x]);var z=o(g,D[0],D[1]);return f(function(){M.hasValue=!0,M.value=z},[z]),h(z),z},bo}var Sp;function Qv(){return Sp||(Sp=1,ho.exports=qv()),ho.exports}Qv();var Vv=V.version.startsWith("19"),Xv=Symbol.for(Vv?"react.transitional.element":"react.element"),Zv=Symbol.for("react.portal"),Iv=Symbol.for("react.fragment"),Kv=Symbol.for("react.strict_mode"),Jv=Symbol.for("react.profiler"),$v=Symbol.for("react.consumer"),kv=Symbol.for("react.context"),cg=Symbol.for("react.forward_ref"),Wv=Symbol.for("react.suspense"),Pv=Symbol.for("react.suspense_list"),qo=Symbol.for("react.memo"),Fv=Symbol.for("react.lazy"),ey=cg,ty=qo;function ny(l){if(typeof l=="object"&&l!==null){const{$$typeof:r}=l;switch(r){case Xv:switch(l=l.type,l){case Iv:case Jv:case Kv:case Wv:case Pv:return l;default:switch(l=l&&l.$$typeof,l){case kv:case cg:case Fv:case qo:return l;case $v:return l;default:return r}}case Zv:return r}}}function ly(l){return ny(l)===qo}function ay(l,r,u,o,{areStatesEqual:s,areOwnPropsEqual:f,areStatePropsEqual:d}){let h=!1,g,p,y,v,x;function D(Q,K){return g=Q,p=K,y=l(g,p),v=r(o,p),x=u(y,v,p),h=!0,x}function M(){return y=l(g,p),r.dependsOnOwnProps&&(v=r(o,p)),x=u(y,v,p),x}function z(){return l.dependsOnOwnProps&&(y=l(g,p)),r.dependsOnOwnProps&&(v=r(o,p)),x=u(y,v,p),x}function O(){const Q=l(g,p),K=!d(Q,y);return y=Q,K&&(x=u(y,v,p)),x}function Y(Q,K){const q=!f(K,p),H=!s(Q,g,K,p);return g=Q,p=K,q&&H?M():q?z():H?O():x}return function(K,q){return h?Y(K,q):D(K,q)}}function ry(l,{initMapStateToProps:r,initMapDispatchToProps:u,initMergeProps:o,...s}){const f=r(l,s),d=u(l,s),h=o(l,s);return ay(f,d,h,l,s)}function iy(l,r){const u={};for(const o in l){const s=l[o];typeof s=="function"&&(u[o]=(...f)=>r(s(...f)))}return u}function wo(l){return function(u){const o=l(u);function s(){return o}return s.dependsOnOwnProps=!1,s}}function Ep(l){return l.dependsOnOwnProps?!!l.dependsOnOwnProps:l.length!==1}function og(l,r){return function(o,{displayName:s}){const f=function(h,g){return f.dependsOnOwnProps?f.mapToProps(h,g):f.mapToProps(h,void 0)};return f.dependsOnOwnProps=!0,f.mapToProps=function(h,g){f.mapToProps=l,f.dependsOnOwnProps=Ep(l);let p=f(h,g);return typeof p=="function"&&(f.mapToProps=p,f.dependsOnOwnProps=Ep(p),p=f(h,g)),p},f}}function Qo(l,r){return(u,o)=>{throw new Error(`Invalid value of type ${typeof l} for ${r} argument when connecting component ${o.wrappedComponentName}.`)}}function uy(l){return l&&typeof l=="object"?wo(r=>iy(l,r)):l?typeof l=="function"?og(l):Qo(l,"mapDispatchToProps"):wo(r=>({dispatch:r}))}function cy(l){return l?typeof l=="function"?og(l):Qo(l,"mapStateToProps"):wo(()=>({}))}function oy(l,r,u){return{...u,...l,...r}}function sy(l){return function(u,{displayName:o,areMergedPropsEqual:s}){let f=!1,d;return function(g,p,y){const v=l(g,p,y);return f?s(v,d)||(d=v):(f=!0,d=v),d}}}function fy(l){return l?typeof l=="function"?sy(l):Qo(l,"mergeProps"):()=>oy}function dy(l){l()}function my(){let l=null,r=null;return{clear(){l=null,r=null},notify(){dy(()=>{let u=l;for(;u;)u.callback(),u=u.next})},get(){const u=[];let o=l;for(;o;)u.push(o),o=o.next;return u},subscribe(u){let o=!0;const s=r={callback:u,next:null,prev:r};return s.prev?s.prev.next=s:l=s,function(){!o||l===null||(o=!1,s.next?s.next.prev=s.prev:r=s.prev,s.prev?s.prev.next=s.next:l=s.next)}}}}var Dp={notify(){},get:()=>[]};function sg(l,r){let u,o=Dp,s=0,f=!1;function d(z){y();const O=o.subscribe(z);let Y=!1;return()=>{Y||(Y=!0,O(),v())}}function h(){o.notify()}function g(){M.onStateChange&&M.onStateChange()}function p(){return f}function y(){s++,u||(u=r?r.addNestedSub(g):l.subscribe(g),o=my())}function v(){s--,u&&s===0&&(u(),u=void 0,o.clear(),o=Dp)}function x(){f||(f=!0,y())}function D(){f&&(f=!1,v())}const M={addNestedSub:d,notifyNestedSubs:h,handleChangeWrapper:g,isSubscribed:p,trySubscribe:x,tryUnsubscribe:D,getListeners:()=>o};return M}var py=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",gy=py(),hy=()=>typeof navigator<"u"&&navigator.product==="ReactNative",by=hy(),vy=()=>gy||by?V.useLayoutEffect:V.useEffect,Bi=vy();function Ap(l,r){return l===r?l!==0||r!==0||1/l===1/r:l!==l&&r!==r}function vo(l,r){if(Ap(l,r))return!0;if(typeof l!="object"||l===null||typeof r!="object"||r===null)return!1;const u=Object.keys(l),o=Object.keys(r);if(u.length!==o.length)return!1;for(let s=0;s<u.length;s++)if(!Object.prototype.hasOwnProperty.call(r,u[s])||!Ap(l[u[s]],r[u[s]]))return!1;return!0}var yy={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},xy={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Sy={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},fg={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Ey={[ey]:Sy,[ty]:fg};function Np(l){return ly(l)?fg:Ey[l.$$typeof]||yy}var Dy=Object.defineProperty,Ay=Object.getOwnPropertyNames,Tp=Object.getOwnPropertySymbols,Ny=Object.getOwnPropertyDescriptor,Ty=Object.getPrototypeOf,Op=Object.prototype;function zo(l,r){if(typeof r!="string"){if(Op){const f=Ty(r);f&&f!==Op&&zo(l,f)}let u=Ay(r);Tp&&(u=u.concat(Tp(r)));const o=Np(l),s=Np(r);for(let f=0;f<u.length;++f){const d=u[f];if(!xy[d]&&!(s&&s[d])&&!(o&&o[d])){const h=Ny(r,d);try{Dy(l,d,h)}catch{}}}}return l}var yo=Symbol.for("react-redux-context"),xo=typeof globalThis<"u"?globalThis:{};function Oy(){if(!V.createContext)return{};const l=xo[yo]??(xo[yo]=new Map);let r=l.get(V.createContext);return r||(r=V.createContext(null),l.set(V.createContext,r)),r}var dg=Oy(),Ry=[null,null];function Cy(l,r,u){Bi(()=>l(...r),u)}function My(l,r,u,o,s,f){l.current=o,u.current=!1,s.current&&(s.current=null,f())}function _y(l,r,u,o,s,f,d,h,g,p,y){if(!l)return()=>{};let v=!1,x=null;const D=()=>{if(v||!h.current)return;const z=r.getState();let O,Y;try{O=o(z,s.current)}catch(Q){Y=Q,x=Q}Y||(x=null),O===f.current?d.current||p():(f.current=O,g.current=O,d.current=!0,y())};return u.onStateChange=D,u.trySubscribe(),D(),()=>{if(v=!0,u.tryUnsubscribe(),u.onStateChange=null,x)throw x}}function wy(l,r){return l===r}function zy(l,r,u,{pure:o,areStatesEqual:s=wy,areOwnPropsEqual:f=vo,areStatePropsEqual:d=vo,areMergedPropsEqual:h=vo,forwardRef:g=!1,context:p=dg}={}){const y=p,v=cy(l),x=uy(r),D=fy(u),M=!!l;return O=>{const Y=O.displayName||O.name||"Component",Q=`Connect(${Y})`,K={shouldHandleStateChanges:M,displayName:Q,wrappedComponentName:Y,WrappedComponent:O,initMapStateToProps:v,initMapDispatchToProps:x,initMergeProps:D,areStatesEqual:s,areStatePropsEqual:d,areOwnPropsEqual:f,areMergedPropsEqual:h};function q(re){const[ie,we,ge]=V.useMemo(()=>{const{reactReduxForwardedRef:tt,...Ft}=re;return[re.context,tt,Ft]},[re]),ve=V.useMemo(()=>{let tt=y;return ie!=null&&ie.Consumer,tt},[ie,y]),ye=V.useContext(ve),$e=!!re.store&&!!re.store.getState&&!!re.store.dispatch,Lt=!!ye&&!!ye.store,Oe=$e?re.store:ye.store,B=Lt?ye.getServerState:Oe.getState,I=V.useMemo(()=>ry(Oe.dispatch,K),[Oe]),[F,xe]=V.useMemo(()=>{if(!M)return Ry;const tt=sg(Oe,$e?void 0:ye.subscription),Ft=tt.notifyNestedSubs.bind(tt);return[tt,Ft]},[Oe,$e,ye]),A=V.useMemo(()=>$e?ye:{...ye,subscription:F},[$e,ye,F]),L=V.useRef(void 0),$=V.useRef(ge),Z=V.useRef(void 0),ee=V.useRef(!1),de=V.useRef(!1),le=V.useRef(void 0);Bi(()=>(de.current=!0,()=>{de.current=!1}),[]);const et=V.useMemo(()=>()=>Z.current&&ge===$.current?Z.current:I(Oe.getState(),ge),[Oe,ge]),Ce=V.useMemo(()=>Ft=>F?_y(M,Oe,F,I,$,L,ee,de,Z,xe,Ft):()=>{},[F]);Cy(My,[$,L,ee,ge,Z,xe]);let pt;try{pt=V.useSyncExternalStore(Ce,et,B?()=>I(B(),ge):et)}catch(tt){throw le.current&&(tt.message+=`
The error may be correlated with this previous error:
${le.current.stack}

`),tt}Bi(()=>{le.current=void 0,Z.current=void 0,L.current=pt});const Vn=V.useMemo(()=>V.createElement(O,{...pt,ref:we}),[we,O,pt]);return V.useMemo(()=>M?V.createElement(ve.Provider,{value:A},Vn):Vn,[ve,Vn,A])}const J=V.memo(q);if(J.WrappedComponent=O,J.displayName=q.displayName=Q,g){const ie=V.forwardRef(function(ge,ve){return V.createElement(J,{...ge,reactReduxForwardedRef:ve})});return ie.displayName=Q,ie.WrappedComponent=O,zo(ie,O)}return zo(J,O)}}var mg=zy;function By(l){const{children:r,context:u,serverState:o,store:s}=l,f=V.useMemo(()=>{const g=sg(s);return{store:s,subscription:g,getServerState:o?()=>o:void 0}},[s,o]),d=V.useMemo(()=>s.getState(),[s]);Bi(()=>{const{subscription:g}=f;return g.onStateChange=g.notifyNestedSubs,g.trySubscribe(),d!==s.getState()&&g.notifyNestedSubs(),()=>{g.tryUnsubscribe(),g.onStateChange=void 0}},[f,d]);const h=u||dg;return V.createElement(h.Provider,{value:f},r)}var jy=By,Uy="Invariant failed";function Gy(l,r){throw new Error(Uy)}var Vt=function(r){var u=r.top,o=r.right,s=r.bottom,f=r.left,d=o-f,h=s-u,g={top:u,right:o,bottom:s,left:f,width:d,height:h,x:f,y:u,center:{x:(o+f)/2,y:(s+u)/2}};return g},Vo=function(r,u){return{top:r.top-u.top,left:r.left-u.left,bottom:r.bottom+u.bottom,right:r.right+u.right}},Rp=function(r,u){return{top:r.top+u.top,left:r.left+u.left,bottom:r.bottom-u.bottom,right:r.right-u.right}},Hy=function(r,u){return{top:r.top+u.y,left:r.left+u.x,bottom:r.bottom+u.y,right:r.right+u.x}},So={top:0,right:0,bottom:0,left:0},Xo=function(r){var u=r.borderBox,o=r.margin,s=o===void 0?So:o,f=r.border,d=f===void 0?So:f,h=r.padding,g=h===void 0?So:h,p=Vt(Vo(u,s)),y=Vt(Rp(u,d)),v=Vt(Rp(y,g));return{marginBox:p,borderBox:Vt(u),paddingBox:y,contentBox:v,margin:s,border:d,padding:g}},Ut=function(r){var u=r.slice(0,-2),o=r.slice(-2);if(o!=="px")return 0;var s=Number(u);return isNaN(s)&&Gy(),s},Ly=function(){return{x:window.pageXOffset,y:window.pageYOffset}},ji=function(r,u){var o=r.borderBox,s=r.border,f=r.margin,d=r.padding,h=Hy(o,u);return Xo({borderBox:h,border:s,margin:f,padding:d})},Ui=function(r,u){return u===void 0&&(u=Ly()),ji(r,u)},pg=function(r,u){var o={top:Ut(u.marginTop),right:Ut(u.marginRight),bottom:Ut(u.marginBottom),left:Ut(u.marginLeft)},s={top:Ut(u.paddingTop),right:Ut(u.paddingRight),bottom:Ut(u.paddingBottom),left:Ut(u.paddingLeft)},f={top:Ut(u.borderTopWidth),right:Ut(u.borderRightWidth),bottom:Ut(u.borderBottomWidth),left:Ut(u.borderLeftWidth)};return Xo({borderBox:r,margin:o,padding:s,border:f})},gg=function(r){var u=r.getBoundingClientRect(),o=window.getComputedStyle(r);return pg(u,o)},ur=function(r){var u=[],o=null,s=function(){for(var d=arguments.length,h=new Array(d),g=0;g<d;g++)h[g]=arguments[g];u=h,!o&&(o=requestAnimationFrame(function(){o=null,r.apply(void 0,u)}))};return s.cancel=function(){o&&(cancelAnimationFrame(o),o=null)},s};function Gi(){return Gi=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var o in u)({}).hasOwnProperty.call(u,o)&&(l[o]=u[o])}return l},Gi.apply(null,arguments)}function hg(l,r){}hg.bind(null,"warn");hg.bind(null,"error");function Yn(){}function Yy(l,r){return{...l,...r}}function Gt(l,r,u){const o=r.map(s=>{const f=Yy(u,s.options);return l.addEventListener(s.eventName,s.fn,f),function(){l.removeEventListener(s.eventName,s.fn,f)}});return function(){o.forEach(f=>{f()})}}const qy="Invariant failed";class Hi extends Error{}Hi.prototype.toString=function(){return this.message};function X(l,r){throw new Hi(qy)}class Qy extends He.Component{constructor(...r){super(...r),this.callbacks=null,this.unbind=Yn,this.onWindowError=u=>{const o=this.getCallbacks();o.isDragging()&&o.tryAbort(),u.error instanceof Hi&&u.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=u=>{this.callbacks=u}}componentDidMount(){this.unbind=Gt(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(r){if(r instanceof Hi){this.setState({});return}throw r}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const Vy=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,Li=l=>l+1,Xy=l=>`
  You have lifted an item in position ${Li(l.source.index)}
`,bg=(l,r)=>{const u=l.droppableId===r.droppableId,o=Li(l.index),s=Li(r.index);return u?`
      You have moved the item from position ${o}
      to position ${s}
    `:`
    You have moved the item from position ${o}
    in list ${l.droppableId}
    to list ${r.droppableId}
    in position ${s}
  `},vg=(l,r,u)=>r.droppableId===u.droppableId?`
      The item ${l}
      has been combined with ${u.draggableId}`:`
      The item ${l}
      in list ${r.droppableId}
      has been combined with ${u.draggableId}
      in list ${u.droppableId}
    `,Zy=l=>{const r=l.destination;if(r)return bg(l.source,r);const u=l.combine;return u?vg(l.draggableId,l.source,u):"You are over an area that cannot be dropped on"},Cp=l=>`
  The item has returned to its starting position
  of ${Li(l.index)}
`,Iy=l=>{if(l.reason==="CANCEL")return`
      Movement cancelled.
      ${Cp(l.source)}
    `;const r=l.destination,u=l.combine;return r?`
      You have dropped the item.
      ${bg(l.source,r)}
    `:u?`
      You have dropped the item.
      ${vg(l.draggableId,l.source,u)}
    `:`
    The item has been dropped while not over a drop area.
    ${Cp(l.source)}
  `},zi={dragHandleUsageInstructions:Vy,onDragStart:Xy,onDragUpdate:Zy,onDragEnd:Iy};function Ky(l,r){return!!(l===r||Number.isNaN(l)&&Number.isNaN(r))}function yg(l,r){if(l.length!==r.length)return!1;for(let u=0;u<l.length;u++)if(!Ky(l[u],r[u]))return!1;return!0}function pe(l,r){const u=V.useState(()=>({inputs:r,result:l()}))[0],o=V.useRef(!0),s=V.useRef(u),d=o.current||!!(r&&s.current.inputs&&yg(r,s.current.inputs))?s.current:{inputs:r,result:l()};return V.useEffect(()=>{o.current=!1,s.current=d},[d]),d.result}function P(l,r){return pe(()=>l,r)}const Je={x:0,y:0},Fe=(l,r)=>({x:l.x+r.x,y:l.y+r.y}),At=(l,r)=>({x:l.x-r.x,y:l.y-r.y}),qn=(l,r)=>l.x===r.x&&l.y===r.y,Fl=l=>({x:l.x!==0?-l.x:0,y:l.y!==0?-l.y:0}),sl=(l,r,u=0)=>l==="x"?{x:r,y:u}:{x:u,y:r},cr=(l,r)=>Math.sqrt((r.x-l.x)**2+(r.y-l.y)**2),Mp=(l,r)=>Math.min(...r.map(u=>cr(l,u))),xg=l=>r=>({x:l(r.x),y:l(r.y)});var Jy=(l,r)=>{const u=Vt({top:Math.max(r.top,l.top),right:Math.min(r.right,l.right),bottom:Math.min(r.bottom,l.bottom),left:Math.max(r.left,l.left)});return u.width<=0||u.height<=0?null:u};const pr=(l,r)=>({top:l.top+r.y,left:l.left+r.x,bottom:l.bottom+r.y,right:l.right+r.x}),_p=l=>[{x:l.left,y:l.top},{x:l.right,y:l.top},{x:l.left,y:l.bottom},{x:l.right,y:l.bottom}],$y={top:0,right:0,bottom:0,left:0},ky=(l,r)=>r?pr(l,r.scroll.diff.displacement):l,Wy=(l,r,u)=>u&&u.increasedBy?{...l,[r.end]:l[r.end]+u.increasedBy[r.line]}:l,Py=(l,r)=>r&&r.shouldClipSubject?Jy(r.pageMarginBox,l):Vt(l);var kl=({page:l,withPlaceholder:r,axis:u,frame:o})=>{const s=ky(l.marginBox,o),f=Wy(s,u,r),d=Py(f,o);return{page:l,withPlaceholder:r,active:d}},Zo=(l,r)=>{l.frame||X();const u=l.frame,o=At(r,u.scroll.initial),s=Fl(o),f={...u,scroll:{initial:u.scroll.initial,current:r,diff:{value:o,displacement:s},max:u.scroll.max}},d=kl({page:l.subject.page,withPlaceholder:l.subject.withPlaceholder,axis:l.axis,frame:f});return{...l,frame:f,subject:d}};function Ke(l,r=yg){let u=null;function o(...s){if(u&&u.lastThis===this&&r(s,u.lastArgs))return u.lastResult;const f=l.apply(this,s);return u={lastResult:f,lastArgs:s,lastThis:this},f}return o.clear=function(){u=null},o}const Sg=Ke(l=>l.reduce((r,u)=>(r[u.descriptor.id]=u,r),{})),Eg=Ke(l=>l.reduce((r,u)=>(r[u.descriptor.id]=u,r),{})),Xi=Ke(l=>Object.values(l)),Fy=Ke(l=>Object.values(l));var ea=Ke((l,r)=>Fy(r).filter(o=>l===o.descriptor.droppableId).sort((o,s)=>o.descriptor.index-s.descriptor.index));function Io(l){return l.at&&l.at.type==="REORDER"?l.at.destination:null}function Zi(l){return l.at&&l.at.type==="COMBINE"?l.at.combine:null}var Ii=Ke((l,r)=>r.filter(u=>u.descriptor.id!==l.descriptor.id)),e1=({isMovingForward:l,draggable:r,destination:u,insideDestination:o,previousImpact:s})=>{if(!u.isCombineEnabled||!Io(s))return null;function d(D){const M={type:"COMBINE",combine:{draggableId:D,droppableId:u.descriptor.id}};return{...s,at:M}}const h=s.displaced.all,g=h.length?h[0]:null;if(l)return g?d(g):null;const p=Ii(r,o);if(!g){if(!p.length)return null;const D=p[p.length-1];return d(D.descriptor.id)}const y=p.findIndex(D=>D.descriptor.id===g);y===-1&&X();const v=y-1;if(v<0)return null;const x=p[v];return d(x.descriptor.id)},ta=(l,r)=>l.descriptor.droppableId===r.descriptor.id;const Dg={point:Je,value:0},or={invisible:{},visible:{},all:[]},t1={displaced:or,displacedBy:Dg,at:null};var Ht=(l,r)=>u=>l<=u&&u<=r,Ag=l=>{const r=Ht(l.top,l.bottom),u=Ht(l.left,l.right);return o=>{if(r(o.top)&&r(o.bottom)&&u(o.left)&&u(o.right))return!0;const f=r(o.top)||r(o.bottom),d=u(o.left)||u(o.right);if(f&&d)return!0;const g=o.top<l.top&&o.bottom>l.bottom,p=o.left<l.left&&o.right>l.right;return g&&p?!0:g&&d||p&&f}},n1=l=>{const r=Ht(l.top,l.bottom),u=Ht(l.left,l.right);return o=>r(o.top)&&r(o.bottom)&&u(o.left)&&u(o.right)};const Ko={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},Ng={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var l1=l=>r=>{const u=Ht(r.top,r.bottom),o=Ht(r.left,r.right);return s=>l===Ko?u(s.top)&&u(s.bottom):o(s.left)&&o(s.right)};const a1=(l,r)=>{const u=r.frame?r.frame.scroll.diff.displacement:Je;return pr(l,u)},r1=(l,r,u)=>r.subject.active?u(r.subject.active)(l):!1,i1=(l,r,u)=>u(r)(l),Jo=({target:l,destination:r,viewport:u,withDroppableDisplacement:o,isVisibleThroughFrameFn:s})=>{const f=o?a1(l,r):l;return r1(f,r,s)&&i1(f,u,s)},u1=l=>Jo({...l,isVisibleThroughFrameFn:Ag}),Tg=l=>Jo({...l,isVisibleThroughFrameFn:n1}),c1=l=>Jo({...l,isVisibleThroughFrameFn:l1(l.destination.axis)}),o1=(l,r,u)=>{if(typeof u=="boolean")return u;if(!r)return!0;const{invisible:o,visible:s}=r;if(o[l])return!1;const f=s[l];return f?f.shouldAnimate:!0};function s1(l,r){const u=l.page.marginBox,o={top:r.point.y,right:0,bottom:0,left:r.point.x};return Vt(Vo(u,o))}function sr({afterDragging:l,destination:r,displacedBy:u,viewport:o,forceShouldAnimate:s,last:f}){return l.reduce(function(h,g){const p=s1(g,u),y=g.descriptor.id;if(h.all.push(y),!u1({target:p,destination:r,viewport:o,withDroppableDisplacement:!0}))return h.invisible[g.descriptor.id]=!0,h;const x=o1(y,f,s),D={draggableId:y,shouldAnimate:x};return h.visible[y]=D,h},{all:[],visible:{},invisible:{}})}function f1(l,r){if(!l.length)return 0;const u=l[l.length-1].descriptor.index;return r.inHomeList?u:u+1}function wp({insideDestination:l,inHomeList:r,displacedBy:u,destination:o}){const s=f1(l,{inHomeList:r});return{displaced:or,displacedBy:u,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:s}}}}function Yi({draggable:l,insideDestination:r,destination:u,viewport:o,displacedBy:s,last:f,index:d,forceShouldAnimate:h}){const g=ta(l,u);if(d==null)return wp({insideDestination:r,inHomeList:g,displacedBy:s,destination:u});const p=r.find(M=>M.descriptor.index===d);if(!p)return wp({insideDestination:r,inHomeList:g,displacedBy:s,destination:u});const y=Ii(l,r),v=r.indexOf(p),x=y.slice(v);return{displaced:sr({afterDragging:x,destination:u,displacedBy:s,last:f,viewport:o.frame,forceShouldAnimate:h}),displacedBy:s,at:{type:"REORDER",destination:{droppableId:u.descriptor.id,index:d}}}}function Qn(l,r){return!!r.effected[l]}var d1=({isMovingForward:l,destination:r,draggables:u,combine:o,afterCritical:s})=>{if(!r.isCombineEnabled)return null;const f=o.draggableId,h=u[f].descriptor.index;return Qn(f,s)?l?h:h-1:l?h+1:h},m1=({isMovingForward:l,isInHomeList:r,insideDestination:u,location:o})=>{if(!u.length)return null;const s=o.index,f=l?s+1:s-1,d=u[0].descriptor.index,h=u[u.length-1].descriptor.index,g=r?h:h+1;return f<d||f>g?null:f},p1=({isMovingForward:l,isInHomeList:r,draggable:u,draggables:o,destination:s,insideDestination:f,previousImpact:d,viewport:h,afterCritical:g})=>{const p=d.at;if(p||X(),p.type==="REORDER"){const v=m1({isMovingForward:l,isInHomeList:r,location:p.destination,insideDestination:f});return v==null?null:Yi({draggable:u,insideDestination:f,destination:s,viewport:h,last:d.displaced,displacedBy:d.displacedBy,index:v})}const y=d1({isMovingForward:l,destination:s,displaced:d.displaced,draggables:o,combine:p.combine,afterCritical:g});return y==null?null:Yi({draggable:u,insideDestination:f,destination:s,viewport:h,last:d.displaced,displacedBy:d.displacedBy,index:y})},g1=({displaced:l,afterCritical:r,combineWith:u,displacedBy:o})=>{const s=!!(l.visible[u]||l.invisible[u]);return Qn(u,r)?s?Je:Fl(o.point):s?o.point:Je},h1=({afterCritical:l,impact:r,draggables:u})=>{const o=Zi(r);o||X();const s=o.draggableId,f=u[s].page.borderBox.center,d=g1({displaced:r.displaced,afterCritical:l,combineWith:s,displacedBy:r.displacedBy});return Fe(f,d)};const Og=(l,r)=>r.margin[l.start]+r.borderBox[l.size]/2,b1=(l,r)=>r.margin[l.end]+r.borderBox[l.size]/2,$o=(l,r,u)=>r[l.crossAxisStart]+u.margin[l.crossAxisStart]+u.borderBox[l.crossAxisSize]/2,zp=({axis:l,moveRelativeTo:r,isMoving:u})=>sl(l.line,r.marginBox[l.end]+Og(l,u),$o(l,r.marginBox,u)),Bp=({axis:l,moveRelativeTo:r,isMoving:u})=>sl(l.line,r.marginBox[l.start]-b1(l,u),$o(l,r.marginBox,u)),v1=({axis:l,moveInto:r,isMoving:u})=>sl(l.line,r.contentBox[l.start]+Og(l,u),$o(l,r.contentBox,u));var y1=({impact:l,draggable:r,draggables:u,droppable:o,afterCritical:s})=>{const f=ea(o.descriptor.id,u),d=r.page,h=o.axis;if(!f.length)return v1({axis:h,moveInto:o.page,isMoving:d});const{displaced:g,displacedBy:p}=l,y=g.all[0];if(y){const x=u[y];if(Qn(y,s))return Bp({axis:h,moveRelativeTo:x.page,isMoving:d});const D=ji(x.page,p.point);return Bp({axis:h,moveRelativeTo:D,isMoving:d})}const v=f[f.length-1];if(v.descriptor.id===r.descriptor.id)return d.borderBox.center;if(Qn(v.descriptor.id,s)){const x=ji(v.page,Fl(s.displacedBy.point));return zp({axis:h,moveRelativeTo:x,isMoving:d})}return zp({axis:h,moveRelativeTo:v.page,isMoving:d})},Bo=(l,r)=>{const u=l.frame;return u?Fe(r,u.scroll.diff.displacement):r};const x1=({impact:l,draggable:r,droppable:u,draggables:o,afterCritical:s})=>{const f=r.page.borderBox.center,d=l.at;return!u||!d?f:d.type==="REORDER"?y1({impact:l,draggable:r,draggables:o,droppable:u,afterCritical:s}):h1({impact:l,draggables:o,afterCritical:s})};var Ki=l=>{const r=x1(l),u=l.droppable;return u?Bo(u,r):r},Rg=(l,r)=>{const u=At(r,l.scroll.initial),o=Fl(u);return{frame:Vt({top:r.y,bottom:r.y+l.frame.height,left:r.x,right:r.x+l.frame.width}),scroll:{initial:l.scroll.initial,max:l.scroll.max,current:r,diff:{value:u,displacement:o}}}};function jp(l,r){return l.map(u=>r[u])}function S1(l,r){for(let u=0;u<r.length;u++){const o=r[u].visible[l];if(o)return o}return null}var E1=({impact:l,viewport:r,destination:u,draggables:o,maxScrollChange:s})=>{const f=Rg(r,Fe(r.scroll.current,s)),d=u.frame?Zo(u,Fe(u.frame.scroll.current,s)):u,h=l.displaced,g=sr({afterDragging:jp(h.all,o),destination:u,displacedBy:l.displacedBy,viewport:f.frame,last:h,forceShouldAnimate:!1}),p=sr({afterDragging:jp(h.all,o),destination:d,displacedBy:l.displacedBy,viewport:r.frame,last:h,forceShouldAnimate:!1}),y={},v={},x=[h,g,p];return h.all.forEach(M=>{const z=S1(M,x);if(z){v[M]=z;return}y[M]=!0}),{...l,displaced:{all:h.all,invisible:y,visible:v}}},D1=(l,r)=>Fe(l.scroll.diff.displacement,r),ko=({pageBorderBoxCenter:l,draggable:r,viewport:u})=>{const o=D1(u,l),s=At(o,r.page.borderBox.center);return Fe(r.client.borderBox.center,s)},Cg=({draggable:l,destination:r,newPageBorderBoxCenter:u,viewport:o,withDroppableDisplacement:s,onlyOnMainAxis:f=!1})=>{const d=At(u,l.page.borderBox.center),g={target:pr(l.page.borderBox,d),destination:r,withDroppableDisplacement:s,viewport:o};return f?c1(g):Tg(g)},A1=({isMovingForward:l,draggable:r,destination:u,draggables:o,previousImpact:s,viewport:f,previousPageBorderBoxCenter:d,previousClientSelection:h,afterCritical:g})=>{if(!u.isEnabled)return null;const p=ea(u.descriptor.id,o),y=ta(r,u),v=e1({isMovingForward:l,draggable:r,destination:u,insideDestination:p,previousImpact:s})||p1({isMovingForward:l,isInHomeList:y,draggable:r,draggables:o,destination:u,insideDestination:p,previousImpact:s,viewport:f,afterCritical:g});if(!v)return null;const x=Ki({impact:v,draggable:r,droppable:u,draggables:o,afterCritical:g});if(Cg({draggable:r,destination:u,newPageBorderBoxCenter:x,viewport:f.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:ko({pageBorderBoxCenter:x,draggable:r,viewport:f}),impact:v,scrollJumpRequest:null};const M=At(x,d),z=E1({impact:v,viewport:f,destination:u,draggables:o,maxScrollChange:M});return{clientSelection:h,impact:z,scrollJumpRequest:M}};const it=l=>{const r=l.subject.active;return r||X(),r};var N1=({isMovingForward:l,pageBorderBoxCenter:r,source:u,droppables:o,viewport:s})=>{const f=u.subject.active;if(!f)return null;const d=u.axis,h=Ht(f[d.start],f[d.end]),g=Xi(o).filter(y=>y!==u).filter(y=>y.isEnabled).filter(y=>!!y.subject.active).filter(y=>Ag(s.frame)(it(y))).filter(y=>{const v=it(y);return l?f[d.crossAxisEnd]<v[d.crossAxisEnd]:v[d.crossAxisStart]<f[d.crossAxisStart]}).filter(y=>{const v=it(y),x=Ht(v[d.start],v[d.end]);return h(v[d.start])||h(v[d.end])||x(f[d.start])||x(f[d.end])}).sort((y,v)=>{const x=it(y)[d.crossAxisStart],D=it(v)[d.crossAxisStart];return l?x-D:D-x}).filter((y,v,x)=>it(y)[d.crossAxisStart]===it(x[0])[d.crossAxisStart]);if(!g.length)return null;if(g.length===1)return g[0];const p=g.filter(y=>Ht(it(y)[d.start],it(y)[d.end])(r[d.line]));return p.length===1?p[0]:p.length>1?p.sort((y,v)=>it(y)[d.start]-it(v)[d.start])[0]:g.sort((y,v)=>{const x=Mp(r,_p(it(y))),D=Mp(r,_p(it(v)));return x!==D?x-D:it(y)[d.start]-it(v)[d.start]})[0]};const Up=(l,r)=>{const u=l.page.borderBox.center;return Qn(l.descriptor.id,r)?At(u,r.displacedBy.point):u},T1=(l,r)=>{const u=l.page.borderBox;return Qn(l.descriptor.id,r)?pr(u,Fl(r.displacedBy.point)):u};var O1=({pageBorderBoxCenter:l,viewport:r,destination:u,insideDestination:o,afterCritical:s})=>o.filter(d=>Tg({target:T1(d,s),destination:u,viewport:r.frame,withDroppableDisplacement:!0})).sort((d,h)=>{const g=cr(l,Bo(u,Up(d,s))),p=cr(l,Bo(u,Up(h,s)));return g<p?-1:p<g?1:d.descriptor.index-h.descriptor.index})[0]||null,gr=Ke(function(r,u){const o=u[r.line];return{value:o,point:sl(r.line,o)}});const R1=(l,r,u)=>{const o=l.axis;if(l.descriptor.mode==="virtual")return sl(o.line,r[o.line]);const s=l.subject.page.contentBox[o.size],g=ea(l.descriptor.id,u).reduce((p,y)=>p+y.client.marginBox[o.size],0)+r[o.line]-s;return g<=0?null:sl(o.line,g)},Mg=(l,r)=>({...l,scroll:{...l.scroll,max:r}}),_g=(l,r,u)=>{const o=l.frame;ta(r,l)&&X(),l.subject.withPlaceholder&&X();const s=gr(l.axis,r.displaceBy).point,f=R1(l,s,u),d={placeholderSize:s,increasedBy:f,oldFrameMaxScroll:l.frame?l.frame.scroll.max:null};if(!o){const y=kl({page:l.subject.page,withPlaceholder:d,axis:l.axis,frame:l.frame});return{...l,subject:y}}const h=f?Fe(o.scroll.max,f):o.scroll.max,g=Mg(o,h),p=kl({page:l.subject.page,withPlaceholder:d,axis:l.axis,frame:g});return{...l,subject:p,frame:g}},C1=l=>{const r=l.subject.withPlaceholder;r||X();const u=l.frame;if(!u){const d=kl({page:l.subject.page,axis:l.axis,frame:null,withPlaceholder:null});return{...l,subject:d}}const o=r.oldFrameMaxScroll;o||X();const s=Mg(u,o),f=kl({page:l.subject.page,axis:l.axis,frame:s,withPlaceholder:null});return{...l,subject:f,frame:s}};var M1=({previousPageBorderBoxCenter:l,moveRelativeTo:r,insideDestination:u,draggable:o,draggables:s,destination:f,viewport:d,afterCritical:h})=>{if(!r){if(u.length)return null;const v={displaced:or,displacedBy:Dg,at:{type:"REORDER",destination:{droppableId:f.descriptor.id,index:0}}},x=Ki({impact:v,draggable:o,droppable:f,draggables:s,afterCritical:h}),D=ta(o,f)?f:_g(f,o,s);return Cg({draggable:o,destination:D,newPageBorderBoxCenter:x,viewport:d.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?v:null}const g=l[f.axis.line]<=r.page.borderBox.center[f.axis.line],p=(()=>{const v=r.descriptor.index;return r.descriptor.id===o.descriptor.id||g?v:v+1})(),y=gr(f.axis,o.displaceBy);return Yi({draggable:o,insideDestination:u,destination:f,viewport:d,displacedBy:y,last:or,index:p})},_1=({isMovingForward:l,previousPageBorderBoxCenter:r,draggable:u,isOver:o,draggables:s,droppables:f,viewport:d,afterCritical:h})=>{const g=N1({isMovingForward:l,pageBorderBoxCenter:r,source:o,droppables:f,viewport:d});if(!g)return null;const p=ea(g.descriptor.id,s),y=O1({pageBorderBoxCenter:r,viewport:d,destination:g,insideDestination:p,afterCritical:h}),v=M1({previousPageBorderBoxCenter:r,destination:g,draggable:u,draggables:s,moveRelativeTo:y,insideDestination:p,viewport:d,afterCritical:h});if(!v)return null;const x=Ki({impact:v,draggable:u,droppable:g,draggables:s,afterCritical:h});return{clientSelection:ko({pageBorderBoxCenter:x,draggable:u,viewport:d}),impact:v,scrollJumpRequest:null}},Nt=l=>{const r=l.at;return r?r.type==="REORDER"?r.destination.droppableId:r.combine.droppableId:null};const w1=(l,r)=>{const u=Nt(l);return u?r[u]:null};var z1=({state:l,type:r})=>{const u=w1(l.impact,l.dimensions.droppables),o=!!u,s=l.dimensions.droppables[l.critical.droppable.id],f=u||s,d=f.axis.direction,h=d==="vertical"&&(r==="MOVE_UP"||r==="MOVE_DOWN")||d==="horizontal"&&(r==="MOVE_LEFT"||r==="MOVE_RIGHT");if(h&&!o)return null;const g=r==="MOVE_DOWN"||r==="MOVE_RIGHT",p=l.dimensions.draggables[l.critical.draggable.id],y=l.current.page.borderBoxCenter,{draggables:v,droppables:x}=l.dimensions;return h?A1({isMovingForward:g,previousPageBorderBoxCenter:y,draggable:p,destination:f,draggables:v,viewport:l.viewport,previousClientSelection:l.current.client.selection,previousImpact:l.impact,afterCritical:l.afterCritical}):_1({isMovingForward:g,previousPageBorderBoxCenter:y,draggable:p,isOver:f,draggables:v,droppables:x,viewport:l.viewport,afterCritical:l.afterCritical})};function ol(l){return l.phase==="DRAGGING"||l.phase==="COLLECTING"}function wg(l){const r=Ht(l.top,l.bottom),u=Ht(l.left,l.right);return function(s){return r(s.y)&&u(s.x)}}function B1(l,r){return l.left<r.right&&l.right>r.left&&l.top<r.bottom&&l.bottom>r.top}function j1({pageBorderBox:l,draggable:r,candidates:u}){const o=r.page.borderBox.center,s=u.map(f=>{const d=f.axis,h=sl(f.axis.line,l.center[d.line],f.page.borderBox.center[d.crossAxisLine]);return{id:f.descriptor.id,distance:cr(o,h)}}).sort((f,d)=>d.distance-f.distance);return s[0]?s[0].id:null}function U1({pageBorderBox:l,draggable:r,droppables:u}){const o=Xi(u).filter(s=>{if(!s.isEnabled)return!1;const f=s.subject.active;if(!f||!B1(l,f))return!1;if(wg(f)(l.center))return!0;const d=s.axis,h=f.center[d.crossAxisLine],g=l[d.crossAxisStart],p=l[d.crossAxisEnd],y=Ht(f[d.crossAxisStart],f[d.crossAxisEnd]),v=y(g),x=y(p);return!v&&!x?!0:v?g<h:p>h});return o.length?o.length===1?o[0].descriptor.id:j1({pageBorderBox:l,draggable:r,candidates:o}):null}const zg=(l,r)=>Vt(pr(l,r));var G1=(l,r)=>{const u=l.frame;return u?zg(r,u.scroll.diff.value):r};function Bg({displaced:l,id:r}){return!!(l.visible[r]||l.invisible[r])}function H1({draggable:l,closest:r,inHomeList:u}){return r?u&&r.descriptor.index>l.descriptor.index?r.descriptor.index-1:r.descriptor.index:null}var L1=({pageBorderBoxWithDroppableScroll:l,draggable:r,destination:u,insideDestination:o,last:s,viewport:f,afterCritical:d})=>{const h=u.axis,g=gr(u.axis,r.displaceBy),p=g.value,y=l[h.start],v=l[h.end],D=Ii(r,o).find(z=>{const O=z.descriptor.id,Y=z.page.borderBox.center[h.line],Q=Qn(O,d),K=Bg({displaced:s,id:O});return Q?K?v<=Y:y<Y-p:K?v<=Y+p:y<Y})||null,M=H1({draggable:r,closest:D,inHomeList:ta(r,u)});return Yi({draggable:r,insideDestination:o,destination:u,viewport:f,last:s,displacedBy:g,index:M})};const Y1=4;var q1=({draggable:l,pageBorderBoxWithDroppableScroll:r,previousImpact:u,destination:o,insideDestination:s,afterCritical:f})=>{if(!o.isCombineEnabled)return null;const d=o.axis,h=gr(o.axis,l.displaceBy),g=h.value,p=r[d.start],y=r[d.end],x=Ii(l,s).find(M=>{const z=M.descriptor.id,O=M.page.borderBox,Q=O[d.size]/Y1,K=Qn(z,f),q=Bg({displaced:u.displaced,id:z});return K?q?y>O[d.start]+Q&&y<O[d.end]-Q:p>O[d.start]-g+Q&&p<O[d.end]-g-Q:q?y>O[d.start]+g+Q&&y<O[d.end]+g-Q:p>O[d.start]+Q&&p<O[d.end]-Q});return x?{displacedBy:h,displaced:u.displaced,at:{type:"COMBINE",combine:{draggableId:x.descriptor.id,droppableId:o.descriptor.id}}}:null},jg=({pageOffset:l,draggable:r,draggables:u,droppables:o,previousImpact:s,viewport:f,afterCritical:d})=>{const h=zg(r.page.borderBox,l),g=U1({pageBorderBox:h,draggable:r,droppables:o});if(!g)return t1;const p=o[g],y=ea(p.descriptor.id,u),v=G1(p,h);return q1({pageBorderBoxWithDroppableScroll:v,draggable:r,previousImpact:s,destination:p,insideDestination:y,afterCritical:d})||L1({pageBorderBoxWithDroppableScroll:v,draggable:r,destination:p,insideDestination:y,last:s.displaced,viewport:f,afterCritical:d})},Wo=(l,r)=>({...l,[r.descriptor.id]:r});const Q1=({previousImpact:l,impact:r,droppables:u})=>{const o=Nt(l),s=Nt(r);if(!o||o===s)return u;const f=u[o];if(!f.subject.withPlaceholder)return u;const d=C1(f);return Wo(u,d)};var V1=({draggable:l,draggables:r,droppables:u,previousImpact:o,impact:s})=>{const f=Q1({previousImpact:o,impact:s,droppables:u}),d=Nt(s);if(!d)return f;const h=u[d];if(ta(l,h)||h.subject.withPlaceholder)return f;const g=_g(h,l,r);return Wo(f,g)},rr=({state:l,clientSelection:r,dimensions:u,viewport:o,impact:s,scrollJumpRequest:f})=>{const d=o||l.viewport,h=u||l.dimensions,g=r||l.current.client.selection,p=At(g,l.initial.client.selection),y={offset:p,selection:g,borderBoxCenter:Fe(l.initial.client.borderBoxCenter,p)},v={selection:Fe(y.selection,d.scroll.current),borderBoxCenter:Fe(y.borderBoxCenter,d.scroll.current),offset:Fe(y.offset,d.scroll.diff.value)},x={client:y,page:v};if(l.phase==="COLLECTING")return{...l,dimensions:h,viewport:d,current:x};const D=h.draggables[l.critical.draggable.id],M=s||jg({pageOffset:v.offset,draggable:D,draggables:h.draggables,droppables:h.droppables,previousImpact:l.impact,viewport:d,afterCritical:l.afterCritical}),z=V1({draggable:D,impact:M,previousImpact:l.impact,draggables:h.draggables,droppables:h.droppables});return{...l,current:x,dimensions:{draggables:h.draggables,droppables:z},impact:M,viewport:d,scrollJumpRequest:f||null,forceShouldAnimate:f?!1:null}};function X1(l,r){return l.map(u=>r[u])}var Ug=({impact:l,viewport:r,draggables:u,destination:o,forceShouldAnimate:s})=>{const f=l.displaced,d=X1(f.all,u),h=sr({afterDragging:d,destination:o,displacedBy:l.displacedBy,viewport:r.frame,forceShouldAnimate:s,last:f});return{...l,displaced:h}},Gg=({impact:l,draggable:r,droppable:u,draggables:o,viewport:s,afterCritical:f})=>{const d=Ki({impact:l,draggable:r,draggables:o,droppable:u,afterCritical:f});return ko({pageBorderBoxCenter:d,draggable:r,viewport:s})},Hg=({state:l,dimensions:r,viewport:u})=>{l.movementMode!=="SNAP"&&X();const o=l.impact,s=u||l.viewport,f=r||l.dimensions,{draggables:d,droppables:h}=f,g=d[l.critical.draggable.id],p=Nt(o);p||X();const y=h[p],v=Ug({impact:o,viewport:s,destination:y,draggables:d}),x=Gg({impact:v,draggable:g,droppable:y,draggables:d,viewport:s,afterCritical:l.afterCritical});return rr({impact:v,clientSelection:x,state:l,dimensions:f,viewport:s})},Z1=l=>({index:l.index,droppableId:l.droppableId}),Lg=({draggable:l,home:r,draggables:u,viewport:o})=>{const s=gr(r.axis,l.displaceBy),f=ea(r.descriptor.id,u),d=f.indexOf(l);d===-1&&X();const h=f.slice(d+1),g=h.reduce((x,D)=>(x[D.descriptor.id]=!0,x),{}),p={inVirtualList:r.descriptor.mode==="virtual",displacedBy:s,effected:g};return{impact:{displaced:sr({afterDragging:h,destination:r,displacedBy:s,last:null,viewport:o.frame,forceShouldAnimate:!1}),displacedBy:s,at:{type:"REORDER",destination:Z1(l.descriptor)}},afterCritical:p}},I1=(l,r)=>({draggables:l.draggables,droppables:Wo(l.droppables,r)}),K1=({draggable:l,offset:r,initialWindowScroll:u})=>{const o=ji(l.client,r),s=Ui(o,u);return{...l,placeholder:{...l.placeholder,client:o},client:o,page:s}},J1=l=>{const r=l.frame;return r||X(),r},$1=({additions:l,updatedDroppables:r,viewport:u})=>{const o=u.scroll.diff.value;return l.map(s=>{const f=s.descriptor.droppableId,d=r[f],g=J1(d).scroll.diff.value,p=Fe(o,g);return K1({draggable:s,offset:p,initialWindowScroll:u.scroll.initial})})},k1=({state:l,published:r})=>{const u=r.modified.map(Y=>{const Q=l.dimensions.droppables[Y.droppableId];return Zo(Q,Y.scroll)}),o={...l.dimensions.droppables,...Sg(u)},s=Eg($1({additions:r.additions,updatedDroppables:o,viewport:l.viewport})),f={...l.dimensions.draggables,...s};r.removals.forEach(Y=>{delete f[Y]});const d={droppables:o,draggables:f},h=Nt(l.impact),g=h?d.droppables[h]:null,p=d.draggables[l.critical.draggable.id],y=d.droppables[l.critical.droppable.id],{impact:v,afterCritical:x}=Lg({draggable:p,home:y,draggables:f,viewport:l.viewport}),D=g&&g.isCombineEnabled?l.impact:v,M=jg({pageOffset:l.current.page.offset,draggable:d.draggables[l.critical.draggable.id],draggables:d.draggables,droppables:d.droppables,previousImpact:D,viewport:l.viewport,afterCritical:x}),z={...l,phase:"DRAGGING",impact:M,onLiftImpact:v,dimensions:d,afterCritical:x,forceShouldAnimate:!1};return l.phase==="COLLECTING"?z:{...z,phase:"DROP_PENDING",reason:l.reason,isWaiting:!1}};const jo=l=>l.movementMode==="SNAP",Eo=(l,r,u)=>{const o=I1(l.dimensions,r);return!jo(l)||u?rr({state:l,dimensions:o}):Hg({state:l,dimensions:o})};function Do(l){return l.isDragging&&l.movementMode==="SNAP"?{...l,scrollJumpRequest:null}:l}const Gp={phase:"IDLE",completed:null,shouldFlush:!1};var W1=(l=Gp,r)=>{if(r.type==="FLUSH")return{...Gp,shouldFlush:!0};if(r.type==="INITIAL_PUBLISH"){l.phase!=="IDLE"&&X();const{critical:u,clientSelection:o,viewport:s,dimensions:f,movementMode:d}=r.payload,h=f.draggables[u.draggable.id],g=f.droppables[u.droppable.id],p={selection:o,borderBoxCenter:h.client.borderBox.center,offset:Je},y={client:p,page:{selection:Fe(p.selection,s.scroll.initial),borderBoxCenter:Fe(p.selection,s.scroll.initial),offset:Fe(p.selection,s.scroll.diff.value)}},v=Xi(f.droppables).every(z=>!z.isFixedOnPage),{impact:x,afterCritical:D}=Lg({draggable:h,home:g,draggables:f.draggables,viewport:s});return{phase:"DRAGGING",isDragging:!0,critical:u,movementMode:d,dimensions:f,initial:y,current:y,isWindowScrollAllowed:v,impact:x,afterCritical:D,onLiftImpact:x,viewport:s,scrollJumpRequest:null,forceShouldAnimate:null}}if(r.type==="COLLECTION_STARTING")return l.phase==="COLLECTING"||l.phase==="DROP_PENDING"?l:(l.phase!=="DRAGGING"&&X(),{...l,phase:"COLLECTING"});if(r.type==="PUBLISH_WHILE_DRAGGING")return l.phase==="COLLECTING"||l.phase==="DROP_PENDING"||X(),k1({state:l,published:r.payload});if(r.type==="MOVE"){if(l.phase==="DROP_PENDING")return l;ol(l)||X();const{client:u}=r.payload;return qn(u,l.current.client.selection)?l:rr({state:l,clientSelection:u,impact:jo(l)?l.impact:null})}if(r.type==="UPDATE_DROPPABLE_SCROLL"){if(l.phase==="DROP_PENDING"||l.phase==="COLLECTING")return Do(l);ol(l)||X();const{id:u,newScroll:o}=r.payload,s=l.dimensions.droppables[u];if(!s)return l;const f=Zo(s,o);return Eo(l,f,!1)}if(r.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(l.phase==="DROP_PENDING")return l;ol(l)||X();const{id:u,isEnabled:o}=r.payload,s=l.dimensions.droppables[u];s||X(),s.isEnabled===o&&X();const f={...s,isEnabled:o};return Eo(l,f,!0)}if(r.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(l.phase==="DROP_PENDING")return l;ol(l)||X();const{id:u,isCombineEnabled:o}=r.payload,s=l.dimensions.droppables[u];s||X(),s.isCombineEnabled===o&&X();const f={...s,isCombineEnabled:o};return Eo(l,f,!0)}if(r.type==="MOVE_BY_WINDOW_SCROLL"){if(l.phase==="DROP_PENDING"||l.phase==="DROP_ANIMATING")return l;ol(l)||X(),l.isWindowScrollAllowed||X();const u=r.payload.newScroll;if(qn(l.viewport.scroll.current,u))return Do(l);const o=Rg(l.viewport,u);return jo(l)?Hg({state:l,viewport:o}):rr({state:l,viewport:o})}if(r.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!ol(l))return l;const u=r.payload.maxScroll;if(qn(u,l.viewport.scroll.max))return l;const o={...l.viewport,scroll:{...l.viewport.scroll,max:u}};return{...l,viewport:o}}if(r.type==="MOVE_UP"||r.type==="MOVE_DOWN"||r.type==="MOVE_LEFT"||r.type==="MOVE_RIGHT"){if(l.phase==="COLLECTING"||l.phase==="DROP_PENDING")return l;l.phase!=="DRAGGING"&&X();const u=z1({state:l,type:r.type});return u?rr({state:l,impact:u.impact,clientSelection:u.clientSelection,scrollJumpRequest:u.scrollJumpRequest}):l}if(r.type==="DROP_PENDING"){const u=r.payload.reason;return l.phase!=="COLLECTING"&&X(),{...l,phase:"DROP_PENDING",isWaiting:!0,reason:u}}if(r.type==="DROP_ANIMATE"){const{completed:u,dropDuration:o,newHomeClientOffset:s}=r.payload;return l.phase==="DRAGGING"||l.phase==="DROP_PENDING"||X(),{phase:"DROP_ANIMATING",completed:u,dropDuration:o,newHomeClientOffset:s,dimensions:l.dimensions}}if(r.type==="DROP_COMPLETE"){const{completed:u}=r.payload;return{phase:"IDLE",completed:u,shouldFlush:!1}}return l};function Se(l,r){return l instanceof Object&&"type"in l&&l.type===r}const P1=l=>({type:"BEFORE_INITIAL_CAPTURE",payload:l}),F1=l=>({type:"LIFT",payload:l}),ex=l=>({type:"INITIAL_PUBLISH",payload:l}),tx=l=>({type:"PUBLISH_WHILE_DRAGGING",payload:l}),nx=()=>({type:"COLLECTION_STARTING",payload:null}),lx=l=>({type:"UPDATE_DROPPABLE_SCROLL",payload:l}),ax=l=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:l}),rx=l=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:l}),Yg=l=>({type:"MOVE",payload:l}),ix=l=>({type:"MOVE_BY_WINDOW_SCROLL",payload:l}),ux=l=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:l}),cx=()=>({type:"MOVE_UP",payload:null}),ox=()=>({type:"MOVE_DOWN",payload:null}),sx=()=>({type:"MOVE_RIGHT",payload:null}),fx=()=>({type:"MOVE_LEFT",payload:null}),Po=()=>({type:"FLUSH",payload:null}),dx=l=>({type:"DROP_ANIMATE",payload:l}),Fo=l=>({type:"DROP_COMPLETE",payload:l}),qg=l=>({type:"DROP",payload:l}),mx=l=>({type:"DROP_PENDING",payload:l}),Qg=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var px=l=>({getState:r,dispatch:u})=>o=>s=>{if(!Se(s,"LIFT")){o(s);return}const{id:f,clientSelection:d,movementMode:h}=s.payload,g=r();g.phase==="DROP_ANIMATING"&&u(Fo({completed:g.completed})),r().phase!=="IDLE"&&X(),u(Po()),u(P1({draggableId:f,movementMode:h}));const y={draggableId:f,scrollOptions:{shouldPublishImmediately:h==="SNAP"}},{critical:v,dimensions:x,viewport:D}=l.startPublishing(y);u(ex({critical:v,dimensions:x,clientSelection:d,movementMode:h,viewport:D}))},gx=l=>()=>r=>u=>{Se(u,"INITIAL_PUBLISH")&&l.dragging(),Se(u,"DROP_ANIMATE")&&l.dropping(u.payload.completed.result.reason),(Se(u,"FLUSH")||Se(u,"DROP_COMPLETE"))&&l.resting(),r(u)};const es={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},fr={opacity:{drop:0,combining:.7},scale:{drop:.75}},Vg={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},cl=`${Vg.outOfTheWay}s ${es.outOfTheWay}`,ir={fluid:`opacity ${cl}`,snap:`transform ${cl}, opacity ${cl}`,drop:l=>{const r=`${l}s ${es.drop}`;return`transform ${r}, opacity ${r}`},outOfTheWay:`transform ${cl}`,placeholder:`height ${cl}, width ${cl}, margin ${cl}`},Hp=l=>qn(l,Je)?void 0:`translate(${l.x}px, ${l.y}px)`,Uo={moveTo:Hp,drop:(l,r)=>{const u=Hp(l);if(u)return r?`${u} scale(${fr.scale.drop})`:u}},{minDropTime:Go,maxDropTime:Xg}=Vg,hx=Xg-Go,Lp=1500,bx=.6;var vx=({current:l,destination:r,reason:u})=>{const o=cr(l,r);if(o<=0)return Go;if(o>=Lp)return Xg;const s=o/Lp,f=Go+hx*s,d=u==="CANCEL"?f*bx:f;return Number(d.toFixed(2))},yx=({impact:l,draggable:r,dimensions:u,viewport:o,afterCritical:s})=>{const{draggables:f,droppables:d}=u,h=Nt(l),g=h?d[h]:null,p=d[r.descriptor.droppableId],y=Gg({impact:l,draggable:r,draggables:f,afterCritical:s,droppable:g||p,viewport:o});return At(y,r.client.borderBox.center)},xx=({draggables:l,reason:r,lastImpact:u,home:o,viewport:s,onLiftImpact:f})=>!u.at||r!=="DROP"?{impact:Ug({draggables:l,impact:f,destination:o,viewport:s,forceShouldAnimate:!0}),didDropInsideDroppable:!1}:u.at.type==="REORDER"?{impact:u,didDropInsideDroppable:!0}:{impact:{...u,displaced:or},didDropInsideDroppable:!0};const Sx=({getState:l,dispatch:r})=>u=>o=>{if(!Se(o,"DROP")){u(o);return}const s=l(),f=o.payload.reason;if(s.phase==="COLLECTING"){r(mx({reason:f}));return}if(s.phase==="IDLE")return;s.phase==="DROP_PENDING"&&s.isWaiting&&X(),s.phase==="DRAGGING"||s.phase==="DROP_PENDING"||X();const h=s.critical,g=s.dimensions,p=g.draggables[s.critical.draggable.id],{impact:y,didDropInsideDroppable:v}=xx({reason:f,lastImpact:s.impact,afterCritical:s.afterCritical,onLiftImpact:s.onLiftImpact,home:s.dimensions.droppables[s.critical.droppable.id],viewport:s.viewport,draggables:s.dimensions.draggables}),x=v?Io(y):null,D=v?Zi(y):null,M={index:h.draggable.index,droppableId:h.droppable.id},z={draggableId:p.descriptor.id,type:p.descriptor.type,source:M,reason:f,mode:s.movementMode,destination:x,combine:D},O=yx({impact:y,draggable:p,dimensions:g,viewport:s.viewport,afterCritical:s.afterCritical}),Y={critical:s.critical,afterCritical:s.afterCritical,result:z,impact:y};if(!(!qn(s.current.client.offset,O)||!!z.combine)){r(Fo({completed:Y}));return}const K=vx({current:s.current.client.offset,destination:O,reason:f});r(dx({newHomeClientOffset:O,dropDuration:K,completed:Y}))};var Zg=()=>({x:window.pageXOffset,y:window.pageYOffset});function Ex(l){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:r=>{r.target!==window&&r.target!==window.document||l()}}}function Dx({onWindowScroll:l}){function r(){l(Zg())}const u=ur(r),o=Ex(u);let s=Yn;function f(){return s!==Yn}function d(){f()&&X(),s=Gt(window,[o])}function h(){f()||X(),u.cancel(),s(),s=Yn}return{start:d,stop:h,isActive:f}}const Ax=l=>Se(l,"DROP_COMPLETE")||Se(l,"DROP_ANIMATE")||Se(l,"FLUSH"),Nx=l=>{const r=Dx({onWindowScroll:u=>{l.dispatch(ix({newScroll:u}))}});return u=>o=>{!r.isActive()&&Se(o,"INITIAL_PUBLISH")&&r.start(),r.isActive()&&Ax(o)&&r.stop(),u(o)}};var Tx=l=>{let r=!1,u=!1;const o=setTimeout(()=>{u=!0}),s=f=>{r||u||(r=!0,l(f),clearTimeout(o))};return s.wasCalled=()=>r,s},Ox=()=>{const l=[],r=s=>{const f=l.findIndex(h=>h.timerId===s);f===-1&&X();const[d]=l.splice(f,1);d.callback()};return{add:s=>{const f=setTimeout(()=>r(f)),d={timerId:f,callback:s};l.push(d)},flush:()=>{if(!l.length)return;const s=[...l];l.length=0,s.forEach(f=>{clearTimeout(f.timerId),f.callback()})}}};const Rx=(l,r)=>l==null&&r==null?!0:l==null||r==null?!1:l.droppableId===r.droppableId&&l.index===r.index,Cx=(l,r)=>l==null&&r==null?!0:l==null||r==null?!1:l.draggableId===r.draggableId&&l.droppableId===r.droppableId,Mx=(l,r)=>{if(l===r)return!0;const u=l.draggable.id===r.draggable.id&&l.draggable.droppableId===r.draggable.droppableId&&l.draggable.type===r.draggable.type&&l.draggable.index===r.draggable.index,o=l.droppable.id===r.droppable.id&&l.droppable.type===r.droppable.type;return u&&o},nr=(l,r)=>{r()},Mi=(l,r)=>({draggableId:l.draggable.id,type:l.droppable.type,source:{droppableId:l.droppable.id,index:l.draggable.index},mode:r});function Ao(l,r,u,o){if(!l){u(o(r));return}const s=Tx(u);l(r,{announce:s}),s.wasCalled()||u(o(r))}var _x=(l,r)=>{const u=Ox();let o=null;const s=(v,x)=>{o&&X(),nr("onBeforeCapture",()=>{const D=l().onBeforeCapture;D&&D({draggableId:v,mode:x})})},f=(v,x)=>{o&&X(),nr("onBeforeDragStart",()=>{const D=l().onBeforeDragStart;D&&D(Mi(v,x))})},d=(v,x)=>{o&&X();const D=Mi(v,x);o={mode:x,lastCritical:v,lastLocation:D.source,lastCombine:null},u.add(()=>{nr("onDragStart",()=>Ao(l().onDragStart,D,r,zi.onDragStart))})},h=(v,x)=>{const D=Io(x),M=Zi(x);o||X();const z=!Mx(v,o.lastCritical);z&&(o.lastCritical=v);const O=!Rx(o.lastLocation,D);O&&(o.lastLocation=D);const Y=!Cx(o.lastCombine,M);if(Y&&(o.lastCombine=M),!z&&!O&&!Y)return;const Q={...Mi(v,o.mode),combine:M,destination:D};u.add(()=>{nr("onDragUpdate",()=>Ao(l().onDragUpdate,Q,r,zi.onDragUpdate))})},g=()=>{o||X(),u.flush()},p=v=>{o||X(),o=null,nr("onDragEnd",()=>Ao(l().onDragEnd,v,r,zi.onDragEnd))};return{beforeCapture:s,beforeStart:f,start:d,update:h,flush:g,drop:p,abort:()=>{if(!o)return;const v={...Mi(o.lastCritical,o.mode),combine:null,destination:null,reason:"CANCEL"};p(v)}}},wx=(l,r)=>{const u=_x(l,r);return o=>s=>f=>{if(Se(f,"BEFORE_INITIAL_CAPTURE")){u.beforeCapture(f.payload.draggableId,f.payload.movementMode);return}if(Se(f,"INITIAL_PUBLISH")){const h=f.payload.critical;u.beforeStart(h,f.payload.movementMode),s(f),u.start(h,f.payload.movementMode);return}if(Se(f,"DROP_COMPLETE")){const h=f.payload.completed.result;u.flush(),s(f),u.drop(h);return}if(s(f),Se(f,"FLUSH")){u.abort();return}const d=o.getState();d.phase==="DRAGGING"&&u.update(d.critical,d.impact)}};const zx=l=>r=>u=>{if(!Se(u,"DROP_ANIMATION_FINISHED")){r(u);return}const o=l.getState();o.phase!=="DROP_ANIMATING"&&X(),l.dispatch(Fo({completed:o.completed}))},Bx=l=>{let r=null,u=null;function o(){u&&(cancelAnimationFrame(u),u=null),r&&(r(),r=null)}return s=>f=>{if((Se(f,"FLUSH")||Se(f,"DROP_COMPLETE")||Se(f,"DROP_ANIMATION_FINISHED"))&&o(),s(f),!Se(f,"DROP_ANIMATE"))return;const d={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){l.getState().phase==="DROP_ANIMATING"&&l.dispatch(Qg())}};u=requestAnimationFrame(()=>{u=null,r=Gt(window,[d])})}};var jx=l=>()=>r=>u=>{(Se(u,"DROP_COMPLETE")||Se(u,"FLUSH")||Se(u,"DROP_ANIMATE"))&&l.stopPublishing(),r(u)},Ux=l=>{let r=!1;return()=>u=>o=>{if(Se(o,"INITIAL_PUBLISH")){r=!0,l.tryRecordFocus(o.payload.critical.draggable.id),u(o),l.tryRestoreFocusRecorded();return}if(u(o),!!r){if(Se(o,"FLUSH")){r=!1,l.tryRestoreFocusRecorded();return}if(Se(o,"DROP_COMPLETE")){r=!1;const s=o.payload.completed.result;s.combine&&l.tryShiftRecord(s.draggableId,s.combine.draggableId),l.tryRestoreFocusRecorded()}}}};const Gx=l=>Se(l,"DROP_COMPLETE")||Se(l,"DROP_ANIMATE")||Se(l,"FLUSH");var Hx=l=>r=>u=>o=>{if(Gx(o)){l.stop(),u(o);return}if(Se(o,"INITIAL_PUBLISH")){u(o);const s=r.getState();s.phase!=="DRAGGING"&&X(),l.start(s);return}u(o),l.scroll(r.getState())};const Lx=l=>r=>u=>{if(r(u),!Se(u,"PUBLISH_WHILE_DRAGGING"))return;const o=l.getState();o.phase==="DROP_PENDING"&&(o.isWaiting||l.dispatch(qg({reason:o.reason})))},Yx=ug;var qx=({dimensionMarshal:l,focusMarshal:r,styleMarshal:u,getResponders:o,announce:s,autoScroller:f})=>ig(W1,Yx(Yv(gx(u),jx(l),px(l),Sx,zx,Bx,Lx,Hx(f),Nx,Ux(r),wx(o,s))));const No=()=>({additions:{},removals:{},modified:{}});function Qx({registry:l,callbacks:r}){let u=No(),o=null;const s=()=>{o||(r.collectionStarting(),o=requestAnimationFrame(()=>{o=null;const{additions:g,removals:p,modified:y}=u,v=Object.keys(g).map(M=>l.draggable.getById(M).getDimension(Je)).sort((M,z)=>M.descriptor.index-z.descriptor.index),x=Object.keys(y).map(M=>{const O=l.droppable.getById(M).callbacks.getScrollWhileDragging();return{droppableId:M,scroll:O}}),D={additions:v,removals:Object.keys(p),modified:x};u=No(),r.publish(D)}))};return{add:g=>{const p=g.descriptor.id;u.additions[p]=g,u.modified[g.descriptor.droppableId]=!0,u.removals[p]&&delete u.removals[p],s()},remove:g=>{const p=g.descriptor;u.removals[p.id]=!0,u.modified[p.droppableId]=!0,u.additions[p.id]&&delete u.additions[p.id],s()},stop:()=>{o&&(cancelAnimationFrame(o),o=null,u=No())}}}var Ig=({scrollHeight:l,scrollWidth:r,height:u,width:o})=>{const s=At({x:r,y:l},{x:o,y:u});return{x:Math.max(0,s.x),y:Math.max(0,s.y)}},Kg=()=>{const l=document.documentElement;return l||X(),l},Jg=()=>{const l=Kg();return Ig({scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth,width:l.clientWidth,height:l.clientHeight})},Vx=()=>{const l=Zg(),r=Jg(),u=l.y,o=l.x,s=Kg(),f=s.clientWidth,d=s.clientHeight,h=o+f,g=u+d;return{frame:Vt({top:u,left:o,right:h,bottom:g}),scroll:{initial:l,current:l,max:r,diff:{value:Je,displacement:Je}}}},Xx=({critical:l,scrollOptions:r,registry:u})=>{const o=Vx(),s=o.scroll.current,f=l.droppable,d=u.droppable.getAllByType(f.type).map(y=>y.callbacks.getDimensionAndWatchScroll(s,r)),h=u.draggable.getAllByType(l.draggable.type).map(y=>y.getDimension(s));return{dimensions:{draggables:Eg(h),droppables:Sg(d)},critical:l,viewport:o}};function Yp(l,r,u){return!(u.descriptor.id===r.id||u.descriptor.type!==r.type||l.droppable.getById(u.descriptor.droppableId).descriptor.mode!=="virtual")}var Zx=(l,r)=>{let u=null;const o=Qx({callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:l}),s=(x,D)=>{l.droppable.exists(x)||X(),u&&r.updateDroppableIsEnabled({id:x,isEnabled:D})},f=(x,D)=>{u&&(l.droppable.exists(x)||X(),r.updateDroppableIsCombineEnabled({id:x,isCombineEnabled:D}))},d=(x,D)=>{u&&(l.droppable.exists(x)||X(),r.updateDroppableScroll({id:x,newScroll:D}))},h=(x,D)=>{u&&l.droppable.getById(x).callbacks.scroll(D)},g=()=>{if(!u)return;o.stop();const x=u.critical.droppable;l.droppable.getAllByType(x.type).forEach(D=>D.callbacks.dragStopped()),u.unsubscribe(),u=null},p=x=>{u||X();const D=u.critical.draggable;x.type==="ADDITION"&&Yp(l,D,x.value)&&o.add(x.value),x.type==="REMOVAL"&&Yp(l,D,x.value)&&o.remove(x.value)};return{updateDroppableIsEnabled:s,updateDroppableIsCombineEnabled:f,scrollDroppable:h,updateDroppableScroll:d,startPublishing:x=>{u&&X();const D=l.draggable.getById(x.draggableId),M=l.droppable.getById(D.descriptor.droppableId),z={draggable:D.descriptor,droppable:M.descriptor},O=l.subscribe(p);return u={critical:z,unsubscribe:O},Xx({critical:z,registry:l,scrollOptions:x.scrollOptions})},stopPublishing:g}},$g=(l,r)=>l.phase==="IDLE"?!0:l.phase!=="DROP_ANIMATING"||l.completed.result.draggableId===r?!1:l.completed.result.reason==="DROP",Ix=l=>{window.scrollBy(l.x,l.y)};const Kx=Ke(l=>Xi(l).filter(r=>!(!r.isEnabled||!r.frame))),Jx=(l,r)=>Kx(r).find(o=>(o.frame||X(),wg(o.frame.pageMarginBox)(l)))||null;var $x=({center:l,destination:r,droppables:u})=>{if(r){const s=u[r];return s.frame?s:null}return Jx(l,u)};const dr={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:l=>l**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var kx=(l,r,u=()=>dr)=>{const o=u(),s=l[r.size]*o.startFromPercentage,f=l[r.size]*o.maxScrollAtPercentage;return{startScrollingFrom:s,maxScrollValueAt:f}},kg=({startOfRange:l,endOfRange:r,current:u})=>{const o=r-l;return o===0?0:(u-l)/o},ts=1,Wx=(l,r,u=()=>dr)=>{const o=u();if(l>r.startScrollingFrom)return 0;if(l<=r.maxScrollValueAt)return o.maxPixelScroll;if(l===r.startScrollingFrom)return ts;const f=1-kg({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:l}),d=o.maxPixelScroll*o.ease(f);return Math.ceil(d)},Px=(l,r,u)=>{const o=u(),s=o.durationDampening.accelerateAt,f=o.durationDampening.stopDampeningAt,d=r,h=f,p=Date.now()-d;if(p>=f)return l;if(p<s)return ts;const y=kg({startOfRange:s,endOfRange:h,current:p}),v=l*o.ease(y);return Math.ceil(v)},qp=({distanceToEdge:l,thresholds:r,dragStartTime:u,shouldUseTimeDampening:o,getAutoScrollerOptions:s})=>{const f=Wx(l,r,s);return f===0?0:o?Math.max(Px(f,u,s),ts):f},Qp=({container:l,distanceToEdges:r,dragStartTime:u,axis:o,shouldUseTimeDampening:s,getAutoScrollerOptions:f})=>{const d=kx(l,o,f);return r[o.end]<r[o.start]?qp({distanceToEdge:r[o.end],thresholds:d,dragStartTime:u,shouldUseTimeDampening:s,getAutoScrollerOptions:f}):-1*qp({distanceToEdge:r[o.start],thresholds:d,dragStartTime:u,shouldUseTimeDampening:s,getAutoScrollerOptions:f})},Fx=({container:l,subject:r,proposedScroll:u})=>{const o=r.height>l.height,s=r.width>l.width;return!s&&!o?u:s&&o?null:{x:s?0:u.x,y:o?0:u.y}};const eS=xg(l=>l===0?0:l);var Wg=({dragStartTime:l,container:r,subject:u,center:o,shouldUseTimeDampening:s,getAutoScrollerOptions:f})=>{const d={top:o.y-r.top,right:r.right-o.x,bottom:r.bottom-o.y,left:o.x-r.left},h=Qp({container:r,distanceToEdges:d,dragStartTime:l,axis:Ko,shouldUseTimeDampening:s,getAutoScrollerOptions:f}),g=Qp({container:r,distanceToEdges:d,dragStartTime:l,axis:Ng,shouldUseTimeDampening:s,getAutoScrollerOptions:f}),p=eS({x:g,y:h});if(qn(p,Je))return null;const y=Fx({container:r,subject:u,proposedScroll:p});return y?qn(y,Je)?null:y:null};const tS=xg(l=>l===0?0:l>0?1:-1),ns=(()=>{const l=(r,u)=>r<0?r:r>u?r-u:0;return({current:r,max:u,change:o})=>{const s=Fe(r,o),f={x:l(s.x,u.x),y:l(s.y,u.y)};return qn(f,Je)?null:f}})(),Pg=({max:l,current:r,change:u})=>{const o={x:Math.max(r.x,l.x),y:Math.max(r.y,l.y)},s=tS(u),f=ns({max:o,current:r,change:s});return!f||s.x!==0&&f.x===0||s.y!==0&&f.y===0},ls=(l,r)=>Pg({current:l.scroll.current,max:l.scroll.max,change:r}),nS=(l,r)=>{if(!ls(l,r))return null;const u=l.scroll.max,o=l.scroll.current;return ns({current:o,max:u,change:r})},as=(l,r)=>{const u=l.frame;return u?Pg({current:u.scroll.current,max:u.scroll.max,change:r}):!1},lS=(l,r)=>{const u=l.frame;return!u||!as(l,r)?null:ns({current:u.scroll.current,max:u.scroll.max,change:r})};var aS=({viewport:l,subject:r,center:u,dragStartTime:o,shouldUseTimeDampening:s,getAutoScrollerOptions:f})=>{const d=Wg({dragStartTime:o,container:l.frame,subject:r,center:u,shouldUseTimeDampening:s,getAutoScrollerOptions:f});return d&&ls(l,d)?d:null},rS=({droppable:l,subject:r,center:u,dragStartTime:o,shouldUseTimeDampening:s,getAutoScrollerOptions:f})=>{const d=l.frame;if(!d)return null;const h=Wg({dragStartTime:o,container:d.pageMarginBox,subject:r,center:u,shouldUseTimeDampening:s,getAutoScrollerOptions:f});return h&&as(l,h)?h:null},Vp=({state:l,dragStartTime:r,shouldUseTimeDampening:u,scrollWindow:o,scrollDroppable:s,getAutoScrollerOptions:f})=>{const d=l.current.page.borderBoxCenter,g=l.dimensions.draggables[l.critical.draggable.id].page.marginBox;if(l.isWindowScrollAllowed){const v=l.viewport,x=aS({dragStartTime:r,viewport:v,subject:g,center:d,shouldUseTimeDampening:u,getAutoScrollerOptions:f});if(x){o(x);return}}const p=$x({center:d,destination:Nt(l.impact),droppables:l.dimensions.droppables});if(!p)return;const y=rS({dragStartTime:r,droppable:p,subject:g,center:d,shouldUseTimeDampening:u,getAutoScrollerOptions:f});y&&s(p.descriptor.id,y)},iS=({scrollWindow:l,scrollDroppable:r,getAutoScrollerOptions:u=()=>dr})=>{const o=ur(l),s=ur(r);let f=null;const d=p=>{f||X();const{shouldUseTimeDampening:y,dragStartTime:v}=f;Vp({state:p,scrollWindow:o,scrollDroppable:s,dragStartTime:v,shouldUseTimeDampening:y,getAutoScrollerOptions:u})};return{start:p=>{f&&X();const y=Date.now();let v=!1;const x=()=>{v=!0};Vp({state:p,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:x,scrollDroppable:x,getAutoScrollerOptions:u}),f={dragStartTime:y,shouldUseTimeDampening:v},v&&d(p)},stop:()=>{f&&(o.cancel(),s.cancel(),f=null)},scroll:d}},uS=({move:l,scrollDroppable:r,scrollWindow:u})=>{const o=(h,g)=>{const p=Fe(h.current.client.selection,g);l({client:p})},s=(h,g)=>{if(!as(h,g))return g;const p=lS(h,g);if(!p)return r(h.descriptor.id,g),null;const y=At(g,p);return r(h.descriptor.id,y),At(g,y)},f=(h,g,p)=>{if(!h||!ls(g,p))return p;const y=nS(g,p);if(!y)return u(p),null;const v=At(p,y);return u(v),At(p,v)};return h=>{const g=h.scrollJumpRequest;if(!g)return;const p=Nt(h.impact);p||X();const y=s(h.dimensions.droppables[p],g);if(!y)return;const v=h.viewport,x=f(h.isWindowScrollAllowed,v,y);x&&o(h,x)}},cS=({scrollDroppable:l,scrollWindow:r,move:u,getAutoScrollerOptions:o})=>{const s=iS({scrollWindow:r,scrollDroppable:l,getAutoScrollerOptions:o}),f=uS({move:u,scrollWindow:r,scrollDroppable:l});return{scroll:g=>{if(!(o().disabled||g.phase!=="DRAGGING")){if(g.movementMode==="FLUID"){s.scroll(g);return}g.scrollJumpRequest&&f(g)}},start:s.start,stop:s.stop}};const Wl="data-rfd",Pl=(()=>{const l=`${Wl}-drag-handle`;return{base:l,draggableId:`${l}-draggable-id`,contextId:`${l}-context-id`}})(),Ho=(()=>{const l=`${Wl}-draggable`;return{base:l,contextId:`${l}-context-id`,id:`${l}-id`}})(),oS=(()=>{const l=`${Wl}-droppable`;return{base:l,contextId:`${l}-context-id`,id:`${l}-id`}})(),Xp={contextId:`${Wl}-scroll-container-context-id`},sS=l=>r=>`[${r}="${l}"]`,lr=(l,r)=>l.map(u=>{const o=u.styles[r];return o?`${u.selector} { ${o} }`:""}).join(" "),fS="pointer-events: none;";var dS=l=>{const r=sS(l),u=(()=>{const h=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:r(Pl.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:h,dragging:fS,dropAnimating:h}}})(),o=(()=>{const h=`
      transition: ${ir.outOfTheWay};
    `;return{selector:r(Ho.contextId),styles:{dragging:h,dropAnimating:h,userCancel:h}}})(),s={selector:r(oS.contextId),styles:{always:"overflow-anchor: none;"}},d=[o,u,s,{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:lr(d,"always"),resting:lr(d,"resting"),dragging:lr(d,"dragging"),dropAnimating:lr(d,"dropAnimating"),userCancel:lr(d,"userCancel")}};const Tt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?V.useLayoutEffect:V.useEffect,To=()=>{const l=document.querySelector("head");return l||X(),l},Zp=l=>{const r=document.createElement("style");return l&&r.setAttribute("nonce",l),r.type="text/css",r};function mS(l,r){const u=pe(()=>dS(l),[l]),o=V.useRef(null),s=V.useRef(null),f=P(Ke(v=>{const x=s.current;x||X(),x.textContent=v}),[]),d=P(v=>{const x=o.current;x||X(),x.textContent=v},[]);Tt(()=>{!o.current&&!s.current||X();const v=Zp(r),x=Zp(r);return o.current=v,s.current=x,v.setAttribute(`${Wl}-always`,l),x.setAttribute(`${Wl}-dynamic`,l),To().appendChild(v),To().appendChild(x),d(u.always),f(u.resting),()=>{const D=M=>{const z=M.current;z||X(),To().removeChild(z),M.current=null};D(o),D(s)}},[r,d,f,u.always,u.resting,l]);const h=P(()=>f(u.dragging),[f,u.dragging]),g=P(v=>{if(v==="DROP"){f(u.dropAnimating);return}f(u.userCancel)},[f,u.dropAnimating,u.userCancel]),p=P(()=>{s.current&&f(u.resting)},[f,u.resting]);return pe(()=>({dragging:h,dropping:g,resting:p}),[h,g,p])}function Fg(l,r){return Array.from(l.querySelectorAll(r))}var eh=l=>l&&l.ownerDocument&&l.ownerDocument.defaultView?l.ownerDocument.defaultView:window;function Ji(l){return l instanceof eh(l).HTMLElement}function pS(l,r){const u=`[${Pl.contextId}="${l}"]`,o=Fg(document,u);if(!o.length)return null;const s=o.find(f=>f.getAttribute(Pl.draggableId)===r);return!s||!Ji(s)?null:s}function gS(l){const r=V.useRef({}),u=V.useRef(null),o=V.useRef(null),s=V.useRef(!1),f=P(function(x,D){const M={id:x,focus:D};return r.current[x]=M,function(){const O=r.current;O[x]!==M&&delete O[x]}},[]),d=P(function(x){const D=pS(l,x);D&&D!==document.activeElement&&D.focus()},[l]),h=P(function(x,D){u.current===x&&(u.current=D)},[]),g=P(function(){o.current||s.current&&(o.current=requestAnimationFrame(()=>{o.current=null;const x=u.current;x&&d(x)}))},[d]),p=P(function(x){u.current=null;const D=document.activeElement;D&&D.getAttribute(Pl.draggableId)===x&&(u.current=x)},[]);return Tt(()=>(s.current=!0,function(){s.current=!1;const x=o.current;x&&cancelAnimationFrame(x)}),[]),pe(()=>({register:f,tryRecordFocus:p,tryRestoreFocusRecorded:g,tryShiftRecord:h}),[f,p,g,h])}function hS(){const l={draggables:{},droppables:{}},r=[];function u(v){return r.push(v),function(){const D=r.indexOf(v);D!==-1&&r.splice(D,1)}}function o(v){r.length&&r.forEach(x=>x(v))}function s(v){return l.draggables[v]||null}function f(v){const x=s(v);return x||X(),x}const d={register:v=>{l.draggables[v.descriptor.id]=v,o({type:"ADDITION",value:v})},update:(v,x)=>{const D=l.draggables[x.descriptor.id];D&&D.uniqueId===v.uniqueId&&(delete l.draggables[x.descriptor.id],l.draggables[v.descriptor.id]=v)},unregister:v=>{const x=v.descriptor.id,D=s(x);D&&v.uniqueId===D.uniqueId&&(delete l.draggables[x],l.droppables[v.descriptor.droppableId]&&o({type:"REMOVAL",value:v}))},getById:f,findById:s,exists:v=>!!s(v),getAllByType:v=>Object.values(l.draggables).filter(x=>x.descriptor.type===v)};function h(v){return l.droppables[v]||null}function g(v){const x=h(v);return x||X(),x}const p={register:v=>{l.droppables[v.descriptor.id]=v},unregister:v=>{const x=h(v.descriptor.id);x&&v.uniqueId===x.uniqueId&&delete l.droppables[v.descriptor.id]},getById:g,findById:h,exists:v=>!!h(v),getAllByType:v=>Object.values(l.droppables).filter(x=>x.descriptor.type===v)};function y(){l.draggables={},l.droppables={},r.length=0}return{draggable:d,droppable:p,subscribe:u,clean:y}}function bS(){const l=pe(hS,[]);return V.useEffect(()=>function(){l.clean()},[l]),l}var rs=He.createContext(null),qi=()=>{const l=document.body;return l||X(),l};const vS={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},yS=l=>`rfd-announcement-${l}`;function xS(l){const r=pe(()=>yS(l),[l]),u=V.useRef(null);return V.useEffect(function(){const f=document.createElement("div");return u.current=f,f.id=r,f.setAttribute("aria-live","assertive"),f.setAttribute("aria-atomic","true"),Gi(f.style,vS),qi().appendChild(f),function(){setTimeout(function(){const g=qi();g.contains(f)&&g.removeChild(f),f===u.current&&(u.current=null)})}},[r]),P(s=>{const f=u.current;if(f){f.textContent=s;return}},[])}const SS={separator:"::"};function is(l,r=SS){const u=He.useId();return pe(()=>`${l}${r.separator}${u}`,[r.separator,l,u])}function ES({contextId:l,uniqueId:r}){return`rfd-hidden-text-${l}-${r}`}function DS({contextId:l,text:r}){const u=is("hidden-text",{separator:"-"}),o=pe(()=>ES({contextId:l,uniqueId:u}),[u,l]);return V.useEffect(function(){const f=document.createElement("div");return f.id=o,f.textContent=r,f.style.display="none",qi().appendChild(f),function(){const h=qi();h.contains(f)&&h.removeChild(f)}},[o,r]),o}var $i=He.createContext(null);function th(l){const r=V.useRef(l);return V.useEffect(()=>{r.current=l}),r}function AS(){let l=null;function r(){return!!l}function u(d){return d===l}function o(d){l&&X();const h={abandon:d};return l=h,h}function s(){l||X(),l=null}function f(){l&&(l.abandon(),s())}return{isClaimed:r,isActive:u,claim:o,release:s,tryAbandon:f}}function mr(l){return l.phase==="IDLE"||l.phase==="DROP_ANIMATING"?!1:l.isDragging}const NS=9,TS=13,us=27,nh=32,OS=33,RS=34,CS=35,MS=36,_S=37,wS=38,zS=39,BS=40,jS={[TS]:!0,[NS]:!0};var lh=l=>{jS[l.keyCode]&&l.preventDefault()};const ki=(()=>{const l="visibilitychange";return typeof document>"u"?l:[l,`ms${l}`,`webkit${l}`,`moz${l}`,`o${l}`].find(o=>`on${o}`in document)||l})(),ah=0,Ip=5;function US(l,r){return Math.abs(r.x-l.x)>=Ip||Math.abs(r.y-l.y)>=Ip}const Kp={type:"IDLE"};function GS({cancel:l,completed:r,getPhase:u,setPhase:o}){return[{eventName:"mousemove",fn:s=>{const{button:f,clientX:d,clientY:h}=s;if(f!==ah)return;const g={x:d,y:h},p=u();if(p.type==="DRAGGING"){s.preventDefault(),p.actions.move(g);return}p.type!=="PENDING"&&X();const y=p.point;if(!US(y,g))return;s.preventDefault();const v=p.actions.fluidLift(g);o({type:"DRAGGING",actions:v})}},{eventName:"mouseup",fn:s=>{const f=u();if(f.type!=="DRAGGING"){l();return}s.preventDefault(),f.actions.drop({shouldBlockNextClick:!0}),r()}},{eventName:"mousedown",fn:s=>{u().type==="DRAGGING"&&s.preventDefault(),l()}},{eventName:"keydown",fn:s=>{if(u().type==="PENDING"){l();return}if(s.keyCode===us){s.preventDefault(),l();return}lh(s)}},{eventName:"resize",fn:l},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{u().type==="PENDING"&&l()}},{eventName:"webkitmouseforcedown",fn:s=>{const f=u();if(f.type==="IDLE"&&X(),f.actions.shouldRespectForcePress()){l();return}s.preventDefault()}},{eventName:ki,fn:l}]}function HS(l){const r=V.useRef(Kp),u=V.useRef(Yn),o=pe(()=>({eventName:"mousedown",fn:function(v){if(v.defaultPrevented||v.button!==ah||v.ctrlKey||v.metaKey||v.shiftKey||v.altKey)return;const x=l.findClosestDraggableId(v);if(!x)return;const D=l.tryGetLock(x,d,{sourceEvent:v});if(!D)return;v.preventDefault();const M={x:v.clientX,y:v.clientY};u.current(),p(D,M)}}),[l]),s=pe(()=>({eventName:"webkitmouseforcewillbegin",fn:y=>{if(y.defaultPrevented)return;const v=l.findClosestDraggableId(y);if(!v)return;const x=l.findOptionsForDraggable(v);x&&(x.shouldRespectForcePress||l.canGetLock(v)&&y.preventDefault())}}),[l]),f=P(function(){const v={passive:!1,capture:!0};u.current=Gt(window,[s,o],v)},[s,o]),d=P(()=>{r.current.type!=="IDLE"&&(r.current=Kp,u.current(),f())},[f]),h=P(()=>{const y=r.current;d(),y.type==="DRAGGING"&&y.actions.cancel({shouldBlockNextClick:!0}),y.type==="PENDING"&&y.actions.abort()},[d]),g=P(function(){const v={capture:!0,passive:!1},x=GS({cancel:h,completed:d,getPhase:()=>r.current,setPhase:D=>{r.current=D}});u.current=Gt(window,x,v)},[h,d]),p=P(function(v,x){r.current.type!=="IDLE"&&X(),r.current={type:"PENDING",point:x,actions:v},g()},[g]);Tt(function(){return f(),function(){u.current()}},[f])}function LS(){}const YS={[RS]:!0,[OS]:!0,[MS]:!0,[CS]:!0};function qS(l,r){function u(){r(),l.cancel()}function o(){r(),l.drop()}return[{eventName:"keydown",fn:s=>{if(s.keyCode===us){s.preventDefault(),u();return}if(s.keyCode===nh){s.preventDefault(),o();return}if(s.keyCode===BS){s.preventDefault(),l.moveDown();return}if(s.keyCode===wS){s.preventDefault(),l.moveUp();return}if(s.keyCode===zS){s.preventDefault(),l.moveRight();return}if(s.keyCode===_S){s.preventDefault(),l.moveLeft();return}if(YS[s.keyCode]){s.preventDefault();return}lh(s)}},{eventName:"mousedown",fn:u},{eventName:"mouseup",fn:u},{eventName:"click",fn:u},{eventName:"touchstart",fn:u},{eventName:"resize",fn:u},{eventName:"wheel",fn:u,options:{passive:!0}},{eventName:ki,fn:u}]}function QS(l){const r=V.useRef(LS),u=pe(()=>({eventName:"keydown",fn:function(f){if(f.defaultPrevented||f.keyCode!==nh)return;const d=l.findClosestDraggableId(f);if(!d)return;const h=l.tryGetLock(d,y,{sourceEvent:f});if(!h)return;f.preventDefault();let g=!0;const p=h.snapLift();r.current();function y(){g||X(),g=!1,r.current(),o()}r.current=Gt(window,qS(p,y),{capture:!0,passive:!1})}}),[l]),o=P(function(){const f={passive:!1,capture:!0};r.current=Gt(window,[u],f)},[u]);Tt(function(){return o(),function(){r.current()}},[o])}const Oo={type:"IDLE"},VS=120,XS=.15;function ZS({cancel:l,getPhase:r}){return[{eventName:"orientationchange",fn:l},{eventName:"resize",fn:l},{eventName:"contextmenu",fn:u=>{u.preventDefault()}},{eventName:"keydown",fn:u=>{if(r().type!=="DRAGGING"){l();return}u.keyCode===us&&u.preventDefault(),l()}},{eventName:ki,fn:l}]}function IS({cancel:l,completed:r,getPhase:u}){return[{eventName:"touchmove",options:{capture:!1},fn:o=>{const s=u();if(s.type!=="DRAGGING"){l();return}s.hasMoved=!0;const{clientX:f,clientY:d}=o.touches[0],h={x:f,y:d};o.preventDefault(),s.actions.move(h)}},{eventName:"touchend",fn:o=>{const s=u();if(s.type!=="DRAGGING"){l();return}o.preventDefault(),s.actions.drop({shouldBlockNextClick:!0}),r()}},{eventName:"touchcancel",fn:o=>{if(u().type!=="DRAGGING"){l();return}o.preventDefault(),l()}},{eventName:"touchforcechange",fn:o=>{const s=u();s.type==="IDLE"&&X();const f=o.touches[0];if(!f||!(f.force>=XS))return;const h=s.actions.shouldRespectForcePress();if(s.type==="PENDING"){h&&l();return}if(h){if(s.hasMoved){o.preventDefault();return}l();return}o.preventDefault()}},{eventName:ki,fn:l}]}function KS(l){const r=V.useRef(Oo),u=V.useRef(Yn),o=P(function(){return r.current},[]),s=P(function(D){r.current=D},[]),f=pe(()=>({eventName:"touchstart",fn:function(D){if(D.defaultPrevented)return;const M=l.findClosestDraggableId(D);if(!M)return;const z=l.tryGetLock(M,h,{sourceEvent:D});if(!z)return;const O=D.touches[0],{clientX:Y,clientY:Q}=O,K={x:Y,y:Q};u.current(),v(z,K)}}),[l]),d=P(function(){const D={capture:!0,passive:!1};u.current=Gt(window,[f],D)},[f]),h=P(()=>{const x=r.current;x.type!=="IDLE"&&(x.type==="PENDING"&&clearTimeout(x.longPressTimerId),s(Oo),u.current(),d())},[d,s]),g=P(()=>{const x=r.current;h(),x.type==="DRAGGING"&&x.actions.cancel({shouldBlockNextClick:!0}),x.type==="PENDING"&&x.actions.abort()},[h]),p=P(function(){const D={capture:!0,passive:!1},M={cancel:g,completed:h,getPhase:o},z=Gt(window,IS(M),D),O=Gt(window,ZS(M),D);u.current=function(){z(),O()}},[g,o,h]),y=P(function(){const D=o();D.type!=="PENDING"&&X();const M=D.actions.fluidLift(D.point);s({type:"DRAGGING",actions:M,hasMoved:!1})},[o,s]),v=P(function(D,M){o().type!=="IDLE"&&X();const z=setTimeout(y,VS);s({type:"PENDING",point:M,actions:D,longPressTimerId:z}),p()},[p,o,s,y]);Tt(function(){return d(),function(){u.current();const M=o();M.type==="PENDING"&&(clearTimeout(M.longPressTimerId),s(Oo))}},[o,d,s]),Tt(function(){return Gt(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}const JS=["input","button","textarea","select","option","optgroup","video","audio"];function rh(l,r){if(r==null)return!1;if(JS.includes(r.tagName.toLowerCase()))return!0;const o=r.getAttribute("contenteditable");return o==="true"||o===""?!0:r===l?!1:rh(l,r.parentElement)}function $S(l,r){const u=r.target;return Ji(u)?rh(l,u):!1}var kS=l=>Vt(l.getBoundingClientRect()).center;function WS(l){return l instanceof eh(l).Element}const PS=(()=>{const l="matches";return typeof document>"u"?l:[l,"msMatchesSelector","webkitMatchesSelector"].find(o=>o in Element.prototype)||l})();function ih(l,r){return l==null?null:l[PS](r)?l:ih(l.parentElement,r)}function FS(l,r){return l.closest?l.closest(r):ih(l,r)}function eE(l){return`[${Pl.contextId}="${l}"]`}function tE(l,r){const u=r.target;if(!WS(u))return null;const o=eE(l),s=FS(u,o);return!s||!Ji(s)?null:s}function nE(l,r){const u=tE(l,r);return u?u.getAttribute(Pl.draggableId):null}function lE(l,r){const u=`[${Ho.contextId}="${l}"]`,s=Fg(document,u).find(f=>f.getAttribute(Ho.id)===r);return!s||!Ji(s)?null:s}function aE(l){l.preventDefault()}function _i({expected:l,phase:r,isLockActive:u,shouldWarn:o}){return!(!u()||l!==r)}function uh({lockAPI:l,store:r,registry:u,draggableId:o}){if(l.isClaimed())return!1;const s=u.draggable.findById(o);return!(!s||!s.options.isEnabled||!$g(r.getState(),o))}function rE({lockAPI:l,contextId:r,store:u,registry:o,draggableId:s,forceSensorStop:f,sourceEvent:d}){if(!uh({lockAPI:l,store:u,registry:o,draggableId:s}))return null;const g=o.draggable.getById(s),p=lE(r,g.descriptor.id);if(!p||d&&!g.options.canDragInteractiveElements&&$S(p,d))return null;const y=l.claim(f||Yn);let v="PRE_DRAG";function x(){return g.options.shouldRespectForcePress}function D(){return l.isActive(y)}function M(H,J){_i({expected:H,phase:v,isLockActive:D,shouldWarn:!0})&&u.dispatch(J())}const z=M.bind(null,"DRAGGING");function O(H){function J(){l.release(),v="COMPLETED"}v!=="PRE_DRAG"&&(J(),X()),u.dispatch(F1(H.liftActionArgs)),v="DRAGGING";function re(ie,we={shouldBlockNextClick:!1}){if(H.cleanup(),we.shouldBlockNextClick){const ge=Gt(window,[{eventName:"click",fn:aE,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(ge)}J(),u.dispatch(qg({reason:ie}))}return{isActive:()=>_i({expected:"DRAGGING",phase:v,isLockActive:D,shouldWarn:!1}),shouldRespectForcePress:x,drop:ie=>re("DROP",ie),cancel:ie=>re("CANCEL",ie),...H.actions}}function Y(H){const J=ur(ie=>{z(()=>Yg({client:ie}))});return{...O({liftActionArgs:{id:s,clientSelection:H,movementMode:"FLUID"},cleanup:()=>J.cancel(),actions:{move:J}}),move:J}}function Q(){const H={moveUp:()=>z(cx),moveRight:()=>z(sx),moveDown:()=>z(ox),moveLeft:()=>z(fx)};return O({liftActionArgs:{id:s,clientSelection:kS(p),movementMode:"SNAP"},cleanup:Yn,actions:H})}function K(){_i({expected:"PRE_DRAG",phase:v,isLockActive:D,shouldWarn:!0})&&l.release()}return{isActive:()=>_i({expected:"PRE_DRAG",phase:v,isLockActive:D,shouldWarn:!1}),shouldRespectForcePress:x,fluidLift:Y,snapLift:Q,abort:K}}const iE=[HS,QS,KS];function uE({contextId:l,store:r,registry:u,customSensors:o,enableDefaultSensors:s}){const f=[...s?iE:[],...o||[]],d=V.useState(()=>AS())[0],h=P(function(O,Y){mr(O)&&!mr(Y)&&d.tryAbandon()},[d]);Tt(function(){let O=r.getState();return r.subscribe(()=>{const Q=r.getState();h(O,Q),O=Q})},[d,r,h]),Tt(()=>d.tryAbandon,[d.tryAbandon]);const g=P(z=>uh({lockAPI:d,registry:u,store:r,draggableId:z}),[d,u,r]),p=P((z,O,Y)=>rE({lockAPI:d,registry:u,contextId:l,store:r,draggableId:z,forceSensorStop:O||null,sourceEvent:Y&&Y.sourceEvent?Y.sourceEvent:null}),[l,d,u,r]),y=P(z=>nE(l,z),[l]),v=P(z=>{const O=u.draggable.findById(z);return O?O.options:null},[u.draggable]),x=P(function(){d.isClaimed()&&(d.tryAbandon(),r.getState().phase!=="IDLE"&&r.dispatch(Po()))},[d,r]),D=P(()=>d.isClaimed(),[d]),M=pe(()=>({canGetLock:g,tryGetLock:p,findClosestDraggableId:y,findOptionsForDraggable:v,tryReleaseLock:x,isLockClaimed:D}),[g,p,y,v,x,D]);for(let z=0;z<f.length;z++)f[z](M)}const cE=l=>({onBeforeCapture:r=>{const u=()=>{l.onBeforeCapture&&l.onBeforeCapture(r)};Yo.flushSync(u)},onBeforeDragStart:l.onBeforeDragStart,onDragStart:l.onDragStart,onDragEnd:l.onDragEnd,onDragUpdate:l.onDragUpdate}),oE=l=>({...dr,...l.autoScrollerOptions,durationDampening:{...dr.durationDampening,...l.autoScrollerOptions}});function ar(l){return l.current||X(),l.current}function sE(l){const{contextId:r,setCallbacks:u,sensors:o,nonce:s,dragHandleUsageInstructions:f}=l,d=V.useRef(null),h=th(l),g=P(()=>cE(h.current),[h]),p=P(()=>oE(h.current),[h]),y=xS(r),v=DS({contextId:r,text:f}),x=mS(r,s),D=P(ge=>{ar(d).dispatch(ge)},[]),M=pe(()=>yp({publishWhileDragging:tx,updateDroppableScroll:lx,updateDroppableIsEnabled:ax,updateDroppableIsCombineEnabled:rx,collectionStarting:nx},D),[D]),z=bS(),O=pe(()=>Zx(z,M),[z,M]),Y=pe(()=>cS({scrollWindow:Ix,scrollDroppable:O.scrollDroppable,getAutoScrollerOptions:p,...yp({move:Yg},D)}),[O.scrollDroppable,D,p]),Q=gS(r),K=pe(()=>qx({announce:y,autoScroller:Y,dimensionMarshal:O,focusMarshal:Q,getResponders:g,styleMarshal:x}),[y,Y,O,Q,g,x]);d.current=K;const q=P(()=>{const ge=ar(d);ge.getState().phase!=="IDLE"&&ge.dispatch(Po())},[]),H=P(()=>{const ge=ar(d).getState();return ge.phase==="DROP_ANIMATING"?!0:ge.phase==="IDLE"?!1:ge.isDragging},[]),J=pe(()=>({isDragging:H,tryAbort:q}),[H,q]);u(J);const re=P(ge=>$g(ar(d).getState(),ge),[]),ie=P(()=>ol(ar(d).getState()),[]),we=pe(()=>({marshal:O,focus:Q,contextId:r,canLift:re,isMovementAllowed:ie,dragHandleUsageInstructionsId:v,registry:z}),[r,O,v,Q,re,ie,z]);return uE({contextId:r,store:K,registry:z,customSensors:o||null,enableDefaultSensors:l.enableDefaultSensors!==!1}),V.useEffect(()=>q,[q]),He.createElement($i.Provider,{value:we},He.createElement(jy,{context:rs,store:K},l.children))}function fE(){return He.useId()}function dE(l){const r=fE(),u=l.dragHandleUsageInstructions||zi.dragHandleUsageInstructions;return He.createElement(Qy,null,o=>He.createElement(sE,{nonce:l.nonce,contextId:r,setCallbacks:o,dragHandleUsageInstructions:u,enableDefaultSensors:l.enableDefaultSensors,sensors:l.sensors,onBeforeCapture:l.onBeforeCapture,onBeforeDragStart:l.onBeforeDragStart,onDragStart:l.onDragStart,onDragUpdate:l.onDragUpdate,onDragEnd:l.onDragEnd,autoScrollerOptions:l.autoScrollerOptions},l.children))}const Jp={dragging:5e3,dropAnimating:4500},mE=(l,r)=>r?ir.drop(r.duration):l?ir.snap:ir.fluid,pE=(l,r)=>{if(l)return r?fr.opacity.drop:fr.opacity.combining},gE=l=>l.forceShouldAnimate!=null?l.forceShouldAnimate:l.mode==="SNAP";function hE(l){const u=l.dimension.client,{offset:o,combineWith:s,dropping:f}=l,d=!!s,h=gE(l),g=!!f,p=g?Uo.drop(o,d):Uo.moveTo(o);return{position:"fixed",top:u.marginBox.top,left:u.marginBox.left,boxSizing:"border-box",width:u.borderBox.width,height:u.borderBox.height,transition:mE(h,f),transform:p,opacity:pE(d,g),zIndex:g?Jp.dropAnimating:Jp.dragging,pointerEvents:"none"}}function bE(l){return{transform:Uo.moveTo(l.offset),transition:l.shouldAnimateDisplacement?void 0:"none"}}function vE(l){return l.type==="DRAGGING"?hE(l):bE(l)}function yE(l,r,u=Je){const o=window.getComputedStyle(r),s=r.getBoundingClientRect(),f=pg(s,o),d=Ui(f,u),h={client:f,tagName:r.tagName.toLowerCase(),display:o.display},g={x:f.marginBox.width,y:f.marginBox.height};return{descriptor:l,placeholder:h,displaceBy:g,client:f,page:d}}function xE(l){const r=is("draggable"),{descriptor:u,registry:o,getDraggableRef:s,canDragInteractiveElements:f,shouldRespectForcePress:d,isEnabled:h}=l,g=pe(()=>({canDragInteractiveElements:f,shouldRespectForcePress:d,isEnabled:h}),[f,h,d]),p=P(D=>{const M=s();return M||X(),yE(u,M,D)},[u,s]),y=pe(()=>({uniqueId:r,descriptor:u,options:g,getDimension:p}),[u,p,g,r]),v=V.useRef(y),x=V.useRef(!0);Tt(()=>(o.draggable.register(v.current),()=>o.draggable.unregister(v.current)),[o.draggable]),Tt(()=>{if(x.current){x.current=!1;return}const D=v.current;v.current=y,o.draggable.update(y,D)},[y,o.draggable])}var cs=He.createContext(null);function Qi(l){const r=V.useContext(l);return r||X(),r}function SE(l){l.preventDefault()}const EE=l=>{const r=V.useRef(null),u=P((J=null)=>{r.current=J},[]),o=P(()=>r.current,[]),{contextId:s,dragHandleUsageInstructionsId:f,registry:d}=Qi($i),{type:h,droppableId:g}=Qi(cs),p=pe(()=>({id:l.draggableId,index:l.index,type:h,droppableId:g}),[l.draggableId,l.index,h,g]),{children:y,draggableId:v,isEnabled:x,shouldRespectForcePress:D,canDragInteractiveElements:M,isClone:z,mapped:O,dropAnimationFinished:Y}=l;if(!z){const J=pe(()=>({descriptor:p,registry:d,getDraggableRef:o,canDragInteractiveElements:M,shouldRespectForcePress:D,isEnabled:x}),[p,d,o,M,D,x]);xE(J)}const Q=pe(()=>x?{tabIndex:0,role:"button","aria-describedby":f,"data-rfd-drag-handle-draggable-id":v,"data-rfd-drag-handle-context-id":s,draggable:!1,onDragStart:SE}:null,[s,f,v,x]),K=P(J=>{O.type==="DRAGGING"&&O.dropping&&J.propertyName==="transform"&&Yo.flushSync(Y)},[Y,O]),q=pe(()=>{const J=vE(O),re=O.type==="DRAGGING"&&O.dropping?K:void 0;return{innerRef:u,draggableProps:{"data-rfd-draggable-context-id":s,"data-rfd-draggable-id":v,style:J,onTransitionEnd:re},dragHandleProps:Q}},[s,Q,v,O,K,u]),H=pe(()=>({draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}),[p.droppableId,p.id,p.index,p.type]);return He.createElement(He.Fragment,null,y(q,O.snapshot,H))};var ch=(l,r)=>l===r,oh=l=>{const{combine:r,destination:u}=l;return u?u.droppableId:r?r.droppableId:null};const DE=l=>l.combine?l.combine.draggableId:null,AE=l=>l.at&&l.at.type==="COMBINE"?l.at.combine.draggableId:null;function NE(){const l=Ke((s,f)=>({x:s,y:f})),r=Ke((s,f,d=null,h=null,g=null)=>({isDragging:!0,isClone:f,isDropAnimating:!!g,dropAnimation:g,mode:s,draggingOver:d,combineWith:h,combineTargetFor:null})),u=Ke((s,f,d,h,g=null,p=null,y=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:g,combineWith:p,mode:f,offset:s,dimension:d,forceShouldAnimate:y,snapshot:r(f,h,g,p,null)}}));return(s,f)=>{if(mr(s)){if(s.critical.draggable.id!==f.draggableId)return null;const d=s.current.client.offset,h=s.dimensions.draggables[f.draggableId],g=Nt(s.impact),p=AE(s.impact),y=s.forceShouldAnimate;return u(l(d.x,d.y),s.movementMode,h,f.isClone,g,p,y)}if(s.phase==="DROP_ANIMATING"){const d=s.completed;if(d.result.draggableId!==f.draggableId)return null;const h=f.isClone,g=s.dimensions.draggables[f.draggableId],p=d.result,y=p.mode,v=oh(p),x=DE(p),M={duration:s.dropDuration,curve:es.drop,moveTo:s.newHomeClientOffset,opacity:x?fr.opacity.drop:null,scale:x?fr.scale.drop:null};return{mapped:{type:"DRAGGING",offset:s.newHomeClientOffset,dimension:g,dropping:M,draggingOver:v,combineWith:x,mode:y,forceShouldAnimate:null,snapshot:r(y,h,v,x,M)}}}return null}}function sh(l=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:l,combineWith:null}}const TE={mapped:{type:"SECONDARY",offset:Je,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:sh(null)}};function OE(){const l=Ke((d,h)=>({x:d,y:h})),r=Ke(sh),u=Ke((d,h=null,g)=>({mapped:{type:"SECONDARY",offset:d,combineTargetFor:h,shouldAnimateDisplacement:g,snapshot:r(h)}})),o=d=>d?u(Je,d,!0):null,s=(d,h,g,p)=>{const y=g.displaced.visible[d],v=!!(p.inVirtualList&&p.effected[d]),x=Zi(g),D=x&&x.draggableId===d?h:null;if(!y){if(!v)return o(D);if(g.displaced.invisible[d])return null;const O=Fl(p.displacedBy.point),Y=l(O.x,O.y);return u(Y,D,!0)}if(v)return o(D);const M=g.displacedBy.point,z=l(M.x,M.y);return u(z,D,y.shouldAnimate)};return(d,h)=>{if(mr(d))return d.critical.draggable.id===h.draggableId?null:s(h.draggableId,d.critical.draggable.id,d.impact,d.afterCritical);if(d.phase==="DROP_ANIMATING"){const g=d.completed;return g.result.draggableId===h.draggableId?null:s(h.draggableId,g.result.draggableId,g.impact,g.afterCritical)}return null}}const RE=()=>{const l=NE(),r=OE();return(o,s)=>l(o,s)||r(o,s)||TE},CE={dropAnimationFinished:Qg},ME=mg(RE,CE,null,{context:rs,areStatePropsEqual:ch})(EE);function fh(l){return Qi(cs).isUsingCloneFor===l.draggableId&&!l.isClone?null:He.createElement(ME,l)}function _E(l){const r=typeof l.isDragDisabled=="boolean"?!l.isDragDisabled:!0,u=!!l.disableInteractiveElementBlocking,o=!!l.shouldRespectForcePress;return He.createElement(fh,Gi({},l,{isClone:!1,isEnabled:r,canDragInteractiveElements:u,shouldRespectForcePress:o}))}const dh=l=>r=>l===r,wE=dh("scroll"),zE=dh("auto"),$p=(l,r)=>r(l.overflowX)||r(l.overflowY),BE=l=>{const r=window.getComputedStyle(l),u={overflowX:r.overflowX,overflowY:r.overflowY};return $p(u,wE)||$p(u,zE)},jE=()=>!1,mh=l=>l==null?null:l===document.body?jE()?l:null:l===document.documentElement?null:BE(l)?l:mh(l.parentElement);var Lo=l=>({x:l.scrollLeft,y:l.scrollTop});const ph=l=>l?window.getComputedStyle(l).position==="fixed"?!0:ph(l.parentElement):!1;var UE=l=>{const r=mh(l),u=ph(l);return{closestScrollable:r,isFixedOnPage:u}},GE=({descriptor:l,isEnabled:r,isCombineEnabled:u,isFixedOnPage:o,direction:s,client:f,page:d,closest:h})=>{const g=(()=>{if(!h)return null;const{scrollSize:x,client:D}=h,M=Ig({scrollHeight:x.scrollHeight,scrollWidth:x.scrollWidth,height:D.paddingBox.height,width:D.paddingBox.width});return{pageMarginBox:h.page.marginBox,frameClient:D,scrollSize:x,shouldClipSubject:h.shouldClipSubject,scroll:{initial:h.scroll,current:h.scroll,max:M,diff:{value:Je,displacement:Je}}}})(),p=s==="vertical"?Ko:Ng,y=kl({page:d,withPlaceholder:null,axis:p,frame:g});return{descriptor:l,isCombineEnabled:u,isFixedOnPage:o,axis:p,isEnabled:r,client:f,page:d,frame:g,subject:y}};const HE=(l,r)=>{const u=gg(l);if(!r||l!==r)return u;const o=u.paddingBox.top-r.scrollTop,s=u.paddingBox.left-r.scrollLeft,f=o+r.scrollHeight,d=s+r.scrollWidth,g=Vo({top:o,right:d,bottom:f,left:s},u.border);return Xo({borderBox:g,margin:u.margin,border:u.border,padding:u.padding})};var LE=({ref:l,descriptor:r,env:u,windowScroll:o,direction:s,isDropDisabled:f,isCombineEnabled:d,shouldClipSubject:h})=>{const g=u.closestScrollable,p=HE(l,g),y=Ui(p,o),v=(()=>{if(!g)return null;const D=gg(g),M={scrollHeight:g.scrollHeight,scrollWidth:g.scrollWidth};return{client:D,page:Ui(D,o),scroll:Lo(g),scrollSize:M,shouldClipSubject:h}})();return GE({descriptor:r,isEnabled:!f,isCombineEnabled:d,isFixedOnPage:u.isFixedOnPage,direction:s,client:p,page:y,closest:v})};const YE={passive:!1},qE={passive:!0};var kp=l=>l.shouldPublishImmediately?YE:qE;const wi=l=>l&&l.env.closestScrollable||null;function QE(l){const r=V.useRef(null),u=Qi($i),o=is("droppable"),{registry:s,marshal:f}=u,d=th(l),h=pe(()=>({id:l.droppableId,type:l.type,mode:l.mode}),[l.droppableId,l.mode,l.type]),g=V.useRef(h),p=pe(()=>Ke((q,H)=>{r.current||X();const J={x:q,y:H};f.updateDroppableScroll(h.id,J)}),[h.id,f]),y=P(()=>{const q=r.current;return!q||!q.env.closestScrollable?Je:Lo(q.env.closestScrollable)},[]),v=P(()=>{const q=y();p(q.x,q.y)},[y,p]),x=pe(()=>ur(v),[v]),D=P(()=>{const q=r.current,H=wi(q);if(q&&H||X(),q.scrollOptions.shouldPublishImmediately){v();return}x()},[x,v]),M=P((q,H)=>{r.current&&X();const J=d.current,re=J.getDroppableRef();re||X();const ie=UE(re),we={ref:re,descriptor:h,env:ie,scrollOptions:H};r.current=we;const ge=LE({ref:re,descriptor:h,env:ie,windowScroll:q,direction:J.direction,isDropDisabled:J.isDropDisabled,isCombineEnabled:J.isCombineEnabled,shouldClipSubject:!J.ignoreContainerClipping}),ve=ie.closestScrollable;return ve&&(ve.setAttribute(Xp.contextId,u.contextId),ve.addEventListener("scroll",D,kp(we.scrollOptions))),ge},[u.contextId,h,D,d]),z=P(()=>{const q=r.current,H=wi(q);return q&&H||X(),Lo(H)},[]),O=P(()=>{const q=r.current;q||X();const H=wi(q);r.current=null,H&&(x.cancel(),H.removeAttribute(Xp.contextId),H.removeEventListener("scroll",D,kp(q.scrollOptions)))},[D,x]),Y=P(q=>{const H=r.current;H||X();const J=wi(H);J||X(),J.scrollTop+=q.y,J.scrollLeft+=q.x},[]),Q=pe(()=>({getDimensionAndWatchScroll:M,getScrollWhileDragging:z,dragStopped:O,scroll:Y}),[O,M,z,Y]),K=pe(()=>({uniqueId:o,descriptor:h,callbacks:Q}),[Q,h,o]);Tt(()=>(g.current=K.descriptor,s.droppable.register(K),()=>{r.current&&O(),s.droppable.unregister(K)}),[Q,h,O,K,f,s.droppable]),Tt(()=>{r.current&&f.updateDroppableIsEnabled(g.current.id,!l.isDropDisabled)},[l.isDropDisabled,f]),Tt(()=>{r.current&&f.updateDroppableIsCombineEnabled(g.current.id,l.isCombineEnabled)},[l.isCombineEnabled,f])}function Ro(){}const Wp={width:0,height:0,margin:$y},VE=({isAnimatingOpenOnMount:l,placeholder:r,animate:u})=>l||u==="close"?Wp:{height:r.client.borderBox.height,width:r.client.borderBox.width,margin:r.client.margin},XE=({isAnimatingOpenOnMount:l,placeholder:r,animate:u})=>{const o=VE({isAnimatingOpenOnMount:l,placeholder:r,animate:u});return{display:r.display,boxSizing:"border-box",width:o.width,height:o.height,marginTop:o.margin.top,marginRight:o.margin.right,marginBottom:o.margin.bottom,marginLeft:o.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:u!=="none"?ir.placeholder:null}},ZE=l=>{const r=V.useRef(null),u=P(()=>{r.current&&(clearTimeout(r.current),r.current=null)},[]),{animate:o,onTransitionEnd:s,onClose:f,contextId:d}=l,[h,g]=V.useState(l.animate==="open");V.useEffect(()=>h?o!=="open"?(u(),g(!1),Ro):r.current?Ro:(r.current=setTimeout(()=>{r.current=null,g(!1)}),u):Ro,[o,h,u]);const p=P(v=>{v.propertyName==="height"&&(s(),o==="close"&&f())},[o,f,s]),y=XE({isAnimatingOpenOnMount:h,animate:l.animate,placeholder:l.placeholder});return He.createElement(l.placeholder.tagName,{style:y,"data-rfd-placeholder-context-id":d,onTransitionEnd:p,ref:l.innerRef})};var IE=He.memo(ZE);class KE extends He.PureComponent{constructor(...r){super(...r),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{this.state.animate==="close"&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(r,u){return r.shouldAnimate?r.on?{isVisible:!0,data:r.on,animate:"open"}:u.isVisible?{isVisible:!0,data:u.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!r.on,data:r.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const r={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(r)}}const JE=l=>{const r=V.useContext($i);r||X();const{contextId:u,isMovementAllowed:o}=r,s=V.useRef(null),f=V.useRef(null),{children:d,droppableId:h,type:g,mode:p,direction:y,ignoreContainerClipping:v,isDropDisabled:x,isCombineEnabled:D,snapshot:M,useClone:z,updateViewportMaxScroll:O,getContainerForClone:Y}=l,Q=P(()=>s.current,[]),K=P((ve=null)=>{s.current=ve},[]);P(()=>f.current,[]);const q=P((ve=null)=>{f.current=ve},[]),H=P(()=>{o()&&O({maxScroll:Jg()})},[o,O]);QE({droppableId:h,type:g,mode:p,direction:y,isDropDisabled:x,isCombineEnabled:D,ignoreContainerClipping:v,getDroppableRef:Q});const J=pe(()=>He.createElement(KE,{on:l.placeholder,shouldAnimate:l.shouldAnimatePlaceholder},({onClose:ve,data:ye,animate:$e})=>He.createElement(IE,{placeholder:ye,onClose:ve,innerRef:q,animate:$e,contextId:u,onTransitionEnd:H})),[u,H,l.placeholder,l.shouldAnimatePlaceholder,q]),re=pe(()=>({innerRef:K,placeholder:J,droppableProps:{"data-rfd-droppable-id":h,"data-rfd-droppable-context-id":u}}),[u,h,J,K]),ie=z?z.dragging.draggableId:null,we=pe(()=>({droppableId:h,type:g,isUsingCloneFor:ie}),[h,ie,g]);function ge(){if(!z)return null;const{dragging:ve,render:ye}=z,$e=He.createElement(fh,{draggableId:ve.draggableId,index:ve.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(Lt,Oe)=>ye(Lt,Oe,ve));return Uv.createPortal($e,Y())}return He.createElement(cs.Provider,{value:we},d(re,M),ge())};function $E(){return document.body||X(),document.body}const Pp={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:$E},gh=l=>{let r={...l},u;for(u in Pp)l[u]===void 0&&(r={...r,[u]:Pp[u]});return r},Co=(l,r)=>l===r.droppable.type,Fp=(l,r)=>r.draggables[l.draggable.id],kE=()=>{const l={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},r={...l,shouldAnimatePlaceholder:!1},u=Ke(f=>({draggableId:f.id,type:f.type,source:{index:f.index,droppableId:f.droppableId}})),o=Ke((f,d,h,g,p,y)=>{const v=p.descriptor.id;if(p.descriptor.droppableId===f){const M=y?{render:y,dragging:u(p.descriptor)}:null,z={isDraggingOver:h,draggingOverWith:h?v:null,draggingFromThisWith:v,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!1,snapshot:z,useClone:M}}if(!d)return r;if(!g)return l;const D={isDraggingOver:h,draggingOverWith:v,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!0,snapshot:D,useClone:null}});return(f,d)=>{const h=gh(d),g=h.droppableId,p=h.type,y=!h.isDropDisabled,v=h.renderClone;if(mr(f)){const x=f.critical;if(!Co(p,x))return r;const D=Fp(x,f.dimensions),M=Nt(f.impact)===g;return o(g,y,M,M,D,v)}if(f.phase==="DROP_ANIMATING"){const x=f.completed;if(!Co(p,x.critical))return r;const D=Fp(x.critical,f.dimensions);return o(g,y,oh(x.result)===g,Nt(x.impact)===g,D,v)}if(f.phase==="IDLE"&&f.completed&&!f.shouldFlush){const x=f.completed;if(!Co(p,x.critical))return r;const D=Nt(x.impact)===g,M=!!(x.impact.at&&x.impact.at.type==="COMBINE"),z=x.critical.droppable.id===g;return D?M?l:r:z?l:r}return r}},WE={updateViewportMaxScroll:ux},PE=mg(kE,WE,(l,r,u)=>({...gh(u),...l,...r}),{context:rs,areStatePropsEqual:ch})(JE),FE=({question:l,onAnswer:r,isAnswered:u,userAnswer:o})=>{const[s,f]=V.useState(o||l.items),[d,h]=V.useState(u),g=D=>{if(!D.destination||u)return;const M=Array.from(s),[z]=M.splice(D.source.index,1);M.splice(D.destination.index,0,z),f(M)},p=()=>{if(d)return;const D=s.every((M,z)=>M.year===l.correctOrder[z].year);h(!0),r(s,D)},y=(D,M)=>{if(!d)return"border-slate-700 hover:border-blue-400";const z=l.correctOrder[M];return D.year===z.year?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"},v=(D,M)=>{if(!d)return null;const z=l.correctOrder[M];return D.year===z.year?S.jsx(Wt,{className:"w-4 h-4 text-green-500"}):S.jsx(Pt,{className:"w-4 h-4 text-red-500"})},x=d?s.filter((D,M)=>D.year===l.correctOrder[M].year).length:0;return S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm text-orange-500 font-medium mb-2",children:"Chronological Order"}),S.jsx("h3",{className:"text-xl font-semibold text-slate-100 mb-2",children:l.question}),l.topic&&S.jsxs("div",{className:"text-sm text-slate-400",children:["Topic: ",l.topic]}),S.jsx("p",{className:"text-slate-400 text-sm mt-2",children:"Drag and drop the events to arrange them in chronological order (earliest to latest)"})]}),S.jsx(dE,{onDragEnd:g,children:S.jsx(PE,{droppableId:"timeline",children:(D,M)=>S.jsxs("div",{...D.droppableProps,ref:D.innerRef,className:`space-y-3 p-4 rounded-lg border-2 border-dashed transition-colors ${M.isDraggingOver?"border-blue-500 bg-blue-500/5":"border-slate-700"}`,children:[s.map((z,O)=>S.jsx(_E,{draggableId:`${z.event}-${z.year}`,index:O,isDragDisabled:u,children:(Y,Q)=>S.jsx("div",{ref:Y.innerRef,...Y.draggableProps,className:`p-4 rounded-lg border-2 transition-all duration-200 ${Q.isDragging?"shadow-lg rotate-2":""} ${y(z,O)} ${u?"cursor-default":"cursor-move"}`,children:S.jsxs("div",{className:"flex items-center gap-3",children:[S.jsx("div",{...Y.dragHandleProps,className:`p-1 rounded ${u?"text-slate-400":"text-slate-400 hover:text-slate-100"}`,children:S.jsx(mv,{className:"w-5 h-5"})}),S.jsxs("div",{className:"flex items-center gap-2 text-slate-400",children:[S.jsx(av,{className:"w-4 h-4"}),S.jsx("span",{className:"text-sm font-mono",children:z.year})]}),S.jsx("div",{className:"flex-1",children:S.jsx("span",{className:"text-slate-100",children:z.event})}),S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsxs("div",{className:"text-sm text-slate-400",children:["#",O+1]}),v(z,O)]})]})})},`${z.event}-${z.year}`)),D.placeholder]})})}),!d&&S.jsx("div",{className:"text-center",children:S.jsx("button",{onClick:p,className:"btn-primary",children:"Submit Order"})}),d&&S.jsxs("div",{className:`p-4 rounded-lg border ${x===l.correctOrder.length?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}`,children:[S.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[x===l.correctOrder.length?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):S.jsx(Pt,{className:"w-5 h-5 text-red-500"}),S.jsx("span",{className:`font-medium ${x===l.correctOrder.length?"text-green-500":"text-red-500"}`,children:x===l.correctOrder.length?"Perfect chronological order!":"Some events are out of order"})]}),S.jsxs("p",{className:"text-slate-400 text-sm mb-3",children:["You got ",x," out of ",l.correctOrder.length," events in the correct position"]}),S.jsxs("div",{className:"space-y-2",children:[S.jsx("h4",{className:"font-medium text-slate-100 text-sm",children:"Correct chronological order:"}),l.correctOrder.map((D,M)=>S.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[S.jsxs("span",{className:"text-slate-400",children:["#",M+1]}),S.jsx("span",{className:"font-mono text-green-500",children:D.year}),S.jsx("span",{className:"text-slate-100",children:D.event})]},M))]})]})]})},e2=({question:l,onAnswer:r,isAnswered:u,userAnswer:o})=>{const[s,f]=V.useState(o||""),[d,h]=V.useState(u),g=()=>{if(d||!s.trim())return;const x=s.trim().toLowerCase()===l.correctAnswer.toLowerCase();h(!0),r(s.trim(),x)},p=x=>{x.key==="Enter"&&!d&&s.trim()&&g()},y=d&&s.trim().toLowerCase()===l.correctAnswer.toLowerCase(),v=l.question.split("______");return S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm text-pink-500 font-medium mb-2",children:"Fill in the Gap"}),S.jsx("h3",{className:"text-xl font-semibold text-slate-100 mb-2",children:"Complete the sentence"}),l.topic&&S.jsxs("div",{className:"text-sm text-slate-400",children:["Topic: ",l.topic," ",l.subtopic&&`• ${l.subtopic}`]})]}),S.jsx("div",{className:"card bg-slate-900 border-slate-700",children:S.jsx("div",{className:"text-lg text-slate-100 leading-relaxed",children:v.map((x,D)=>S.jsxs("span",{children:[x,D<v.length-1&&S.jsx("span",{className:"inline-block",children:d?S.jsx("span",{className:`px-3 py-1 rounded font-medium ${y?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-red-500/20 text-red-400 border border-red-500/30"}`,children:s||"______"}):S.jsx("input",{type:"text",value:s,onChange:M=>f(M.target.value),onKeyPress:p,placeholder:"Type your answer...",className:"input inline-block w-48 mx-1 text-center",disabled:d})})]},D))})}),!d&&S.jsxs("div",{className:"flex items-center gap-4",children:[S.jsxs("button",{onClick:g,disabled:!s.trim(),className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[S.jsx(bv,{className:"w-4 h-4"}),"Submit Answer"]}),S.jsx("p",{className:"text-slate-400 text-sm",children:"Press Enter or click Submit when ready"})]}),d&&S.jsxs("div",{className:`p-4 rounded-lg border ${y?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"}`,children:[S.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[y?S.jsx(Wt,{className:"w-5 h-5 text-green-500"}):S.jsx(Pt,{className:"w-5 h-5 text-red-500"}),S.jsx("span",{className:`font-medium ${y?"text-green-500":"text-red-500"}`,children:y?"Correct!":"Incorrect"})]}),S.jsxs("div",{className:"space-y-2 text-sm",children:[S.jsxs("div",{children:[S.jsx("span",{className:"text-slate-400",children:"Your answer: "}),S.jsx("span",{className:y?"text-green-400":"text-red-400",children:s||"(no answer)"})]}),!y&&S.jsxs("div",{children:[S.jsx("span",{className:"text-slate-400",children:"Correct answer: "}),S.jsx("span",{className:"text-green-400 font-medium",children:l.correctAnswer})]})]})]}),S.jsx("div",{className:"text-center",children:S.jsx("p",{className:"text-slate-400 text-sm",children:"💡 Tip: Look for key terms, dates, names, or important concepts"})})]})},t2=({topic:l,onBack:r})=>{const{currentQuestion:u,score:o,questions:s,answers:f,isComplete:d,timeSpent:h,startQuiz:g,answerQuestion:p,nextQuestion:y,previousQuestion:v,completeQuiz:x,resetQuiz:D}=wv(),[M,z]=V.useState(0);V.useEffect(()=>{const q=(l==null?void 0:l.id)||null;console.log("Generating questions for topicId:",q);const H=pp(q,20);console.log("Generated questions:",H.length,H),H.length===0?(console.error("No questions generated!"),g([{id:"fallback_1",type:"true_false",question:"The Weimar Republic was established in Germany after World War I.",correctAnswer:!0,topic:"History",subtopic:"Test"}])):g(H)},[l]),V.useEffect(()=>{const q=setInterval(()=>{z(H=>H+1)},1e3);return()=>clearInterval(q)},[]);const O=(q,H)=>{p(q,H),setTimeout(()=>{u<s.length-1?y():x()},1e3)},Y=()=>{D();const q=(l==null?void 0:l.id)||null,H=pp(q,20);g(H),z(0)},Q=q=>{const H=Math.floor(q/60),J=q%60;return`${H}:${J.toString().padStart(2,"0")}`},K=()=>{if(!s[u])return null;const q=s[u],H=f[u],J={question:q,onAnswer:O,isAnswered:!!H,userAnswer:H==null?void 0:H.answer};switch(q.type){case kt.MULTIPLE_CHOICE:return S.jsx(zv,{...J});case kt.TRUE_FALSE:return S.jsx(Bv,{...J});case kt.MATCH_UP:return S.jsx(jv,{...J});case kt.DRAG_ORDER:return S.jsx(FE,{...J});case kt.FILL_GAPS:return S.jsx(e2,{...J});default:return S.jsx("div",{children:"Unknown question type"})}};if(s.length===0)return S.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),S.jsx("p",{className:"text-slate-400",children:"Generating questions..."})]})});if(d){const q=Math.round(o/s.length*100),H=q>=80?"Excellent!":q>=60?"Good!":q>=40?"Fair":"Needs Improvement";return S.jsx("div",{className:"max-w-2xl mx-auto",children:S.jsxs("div",{className:"card text-center",children:[S.jsxs("div",{className:"mb-6",children:[q>=60?S.jsx(cv,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}):S.jsx(sv,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),S.jsx("h2",{className:"text-3xl font-bold text-slate-100 mb-2",children:"Quiz Complete!"}),S.jsx("p",{className:"text-slate-400",children:H})]}),S.jsxs("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"text-2xl font-bold text-blue-500",children:o}),S.jsx("div",{className:"text-sm text-slate-400",children:"Correct"})]}),S.jsxs("div",{className:"text-center",children:[S.jsxs("div",{className:"text-2xl font-bold text-slate-100",children:[q,"%"]}),S.jsx("div",{className:"text-sm text-slate-400",children:"Score"})]}),S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"text-2xl font-bold text-slate-100",children:Q(M)}),S.jsx("div",{className:"text-sm text-slate-400",children:"Time"})]})]}),S.jsxs("div",{className:"flex gap-4 justify-center",children:[S.jsx("button",{onClick:Y,className:"btn-primary",children:"Try Again"}),S.jsx("button",{onClick:r,className:"btn-secondary",children:"Back to Topics"})]})]})})}return S.jsxs("div",{className:"max-w-4xl mx-auto",children:[S.jsxs("div",{className:"flex items-center justify-between mb-6",children:[S.jsxs("button",{onClick:r,className:"btn-secondary flex items-center gap-2",children:[S.jsx(Fb,{className:"w-4 h-4"}),"Back to Topics"]}),S.jsxs("div",{className:"flex items-center gap-4 text-slate-400",children:[S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx(ag,{className:"w-4 h-4"}),S.jsx("span",{children:Q(M)})]}),S.jsxs("div",{className:"text-sm",children:["Question ",u+1," of ",s.length]})]})]}),S.jsxs("div",{className:"mb-6",children:[S.jsxs("div",{className:"flex justify-between text-sm text-slate-400 mb-2",children:[S.jsx("span",{children:l?l.title:"Mixed Practice"}),S.jsxs("span",{children:[Math.round((u+1)/s.length*100),"% Complete"]})]}),S.jsx("div",{className:"w-full bg-slate-700 rounded-full h-2",children:S.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(u+1)/s.length*100}%`}})})]}),S.jsx("div",{className:"card",children:K()}),S.jsxs("div",{className:"flex justify-between mt-6",children:[S.jsx("button",{onClick:v,disabled:u===0,className:"btn-secondary disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),S.jsxs("div",{className:"text-center text-slate-400",children:["Score: ",o,"/",u+(f[u]?1:0)]}),S.jsx("button",{onClick:y,disabled:u>=s.length-1||!f[u],className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:u>=s.length-1?"Finish":"Next"})]})]})};function n2(){const[l,r]=V.useState(null),[u,o]=V.useState(!1),s=d=>{r(d),o(!0)},f=()=>{o(!1),r(null)};return S.jsx(_v,{children:S.jsxs("div",{className:"min-h-screen bg-slate-900",children:[S.jsx("header",{className:"bg-slate-800 border-b border-slate-700",children:S.jsx("div",{className:"max-w-6xl mx-auto px-4 py-6",children:S.jsxs("div",{className:"flex items-center gap-3",children:[S.jsx("div",{className:"p-2 bg-blue-600 rounded-lg",children:S.jsx(lg,{className:"w-6 h-6 text-white"})}),S.jsxs("div",{children:[S.jsx("h1",{className:"text-2xl font-bold text-slate-100",children:"History GCSE Revision"}),S.jsx("p",{className:"text-slate-400",children:"Master your knowledge with interactive quizzes"})]})]})})}),S.jsx("main",{className:"max-w-6xl mx-auto px-4 py-8",children:u?S.jsx(t2,{topic:l,onBack:f}):S.jsxs("div",{className:"space-y-8",children:[S.jsxs("div",{className:"text-center space-y-4",children:[S.jsx("h2",{className:"text-3xl font-bold text-slate-100",children:"Welcome to Your Revision Hub"}),S.jsx("p",{className:"text-slate-400 max-w-2xl mx-auto",children:"Test your knowledge with multiple choice questions, true/false, matching exercises, chronological ordering, and fill-in-the-gap challenges."})]}),S.jsxs("div",{className:"grid md:grid-cols-3 gap-6 mb-8",children:[S.jsxs("div",{className:"card text-center",children:[S.jsx(nv,{className:"w-8 h-8 text-blue-500 mx-auto mb-3"}),S.jsx("h3",{className:"font-semibold text-slate-100 mb-2",children:"Multiple Question Types"}),S.jsx("p",{className:"text-slate-400 text-sm",children:"MCQ, True/False, Matching, Ordering & Fill-in-the-gaps"})]}),S.jsxs("div",{className:"card text-center",children:[S.jsx(Mo,{className:"w-8 h-8 text-green-500 mx-auto mb-3"}),S.jsx("h3",{className:"font-semibold text-slate-100 mb-2",children:"Focused Learning"}),S.jsx("p",{className:"text-slate-400 text-sm",children:"Topic-based quizzes covering key GCSE content"})]}),S.jsxs("div",{className:"card text-center",children:[S.jsx(xv,{className:"w-8 h-8 text-yellow-500 mx-auto mb-3"}),S.jsx("h3",{className:"font-semibold text-slate-100 mb-2",children:"Track Progress"}),S.jsx("p",{className:"text-slate-400 text-sm",children:"Monitor your performance and improvement"})]})]}),S.jsx(Cv,{onStartQuiz:s})]})})]})})}Ib.createRoot(document.getElementById("root")).render(S.jsx(V.StrictMode,{children:S.jsx(n2,{})}));

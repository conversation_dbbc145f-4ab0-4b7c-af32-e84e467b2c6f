import { useEffect, useState } from 'react'
import { ArrowLeft, Clock, CheckCircle, XCircle } from 'lucide-react'
import { useQuiz } from '../hooks/useQuiz.jsx'
import { generateQuestions, QUESTION_TYPES } from '../utils/questionGenerator'
import MultipleChoice from './questions/MultipleChoice'
import TrueFalse from './questions/TrueFalse'
import MatchUp from './questions/MatchUp'
import DragOrder from './questions/DragOrder'
import FillGaps from './questions/FillGaps'

const QuizContainer = ({ topic, onBack }) => {
  const {
    currentQuestion,
    score,
    questions,
    answers,
    isComplete,
    timeSpent,
    startQuiz,
    answerQuestion,
    nextQuestion,
    previousQuestion,
    completeQuiz,
    resetQuiz
  } = useQuiz()

  const [timeElapsed, setTimeElapsed] = useState(0)

  useEffect(() => {
    // Generate questions when component mounts
    const topicId = topic?.id || null
    const generatedQuestions = generateQuestions(topicId, 20)
    startQuiz(generatedQuestions)
  }, [topic])

  useEffect(() => {
    // Timer
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const handleAnswer = (answer, isCorrect) => {
    answerQuestion(answer, isCorrect)
    
    // Auto-advance after a short delay
    setTimeout(() => {
      if (currentQuestion < questions.length - 1) {
        nextQuestion()
      } else {
        completeQuiz()
      }
    }, 1000)
  }

  const handleRestart = () => {
    resetQuiz()
    const topicId = topic?.id || null
    const generatedQuestions = generateQuestions(topicId, 20)
    startQuiz(generatedQuestions)
    setTimeElapsed(0)
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const renderQuestion = () => {
    if (!questions[currentQuestion]) return null

    const question = questions[currentQuestion]
    const currentAnswer = answers[currentQuestion]

    const commonProps = {
      question,
      onAnswer: handleAnswer,
      isAnswered: !!currentAnswer,
      userAnswer: currentAnswer?.answer
    }

    switch (question.type) {
      case QUESTION_TYPES.MULTIPLE_CHOICE:
        return <MultipleChoice {...commonProps} />
      case QUESTION_TYPES.TRUE_FALSE:
        return <TrueFalse {...commonProps} />
      case QUESTION_TYPES.MATCH_UP:
        return <MatchUp {...commonProps} />
      case QUESTION_TYPES.DRAG_ORDER:
        return <DragOrder {...commonProps} />
      case QUESTION_TYPES.FILL_GAPS:
        return <FillGaps {...commonProps} />
      default:
        return <div>Unknown question type</div>
    }
  }

  if (questions.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-slate-400">Generating questions...</p>
        </div>
      </div>
    )
  }

  if (isComplete) {
    const percentage = Math.round((score / questions.length) * 100)
    const grade = percentage >= 80 ? 'Excellent!' : percentage >= 60 ? 'Good!' : percentage >= 40 ? 'Fair' : 'Needs Improvement'
    
    return (
      <div className="max-w-2xl mx-auto">
        <div className="card text-center">
          <div className="mb-6">
            {percentage >= 60 ? (
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            ) : (
              <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            )}
            <h2 className="text-3xl font-bold text-slate-100 mb-2">Quiz Complete!</h2>
            <p className="text-slate-400">{grade}</p>
          </div>

          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">{score}</div>
              <div className="text-sm text-slate-400">Correct</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-slate-100">{percentage}%</div>
              <div className="text-sm text-slate-400">Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-slate-100">{formatTime(timeElapsed)}</div>
              <div className="text-sm text-slate-400">Time</div>
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <button onClick={handleRestart} className="btn-primary">
              Try Again
            </button>
            <button onClick={onBack} className="btn-secondary">
              Back to Topics
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={onBack}
          className="btn-secondary flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Topics
        </button>
        
        <div className="flex items-center gap-4 text-slate-400">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <span>{formatTime(timeElapsed)}</span>
          </div>
          <div className="text-sm">
            Question {currentQuestion + 1} of {questions.length}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-slate-400 mb-2">
          <span>{topic ? topic.title : 'Mixed Practice'}</span>
          <span>{Math.round(((currentQuestion + 1) / questions.length) * 100)}% Complete</span>
        </div>
        <div className="w-full bg-slate-700 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Question */}
      <div className="card">
        {renderQuestion()}
      </div>

      {/* Navigation */}
      <div className="flex justify-between mt-6">
        <button
          onClick={previousQuestion}
          disabled={currentQuestion === 0}
          className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        
        <div className="text-center text-slate-400">
          Score: {score}/{currentQuestion + (answers[currentQuestion] ? 1 : 0)}
        </div>
        
        <button
          onClick={nextQuestion}
          disabled={currentQuestion >= questions.length - 1 || !answers[currentQuestion]}
          className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {currentQuestion >= questions.length - 1 ? 'Finish' : 'Next'}
        </button>
      </div>
    </div>
  )
}

export default QuizContainer

import { useState } from 'react'
import { Check, X } from 'lucide-react'

const MultipleChoice = ({ question, onAnswer, isAnswered, userAnswer }) => {
  const [selectedOption, setSelectedOption] = useState(userAnswer || null)

  const handleOptionSelect = (option) => {
    if (isAnswered) return
    
    setSelectedOption(option)
    const isCorrect = option === question.correctAnswer
    onAnswer(option, isCorrect)
  }

  const getOptionStyle = (option) => {
    if (!isAnswered) {
      return selectedOption === option
        ? 'border-blue-500 bg-blue-500/10'
        : 'border-slate-700 hover:border-blue-400 hover:bg-blue-400/5'
    }

    if (option === question.correctAnswer) {
      return 'border-green-500 bg-green-500/10'
    }

    if (option === selectedOption && option !== question.correctAnswer) {
      return 'border-red-500 bg-red-500/10'
    }

    return 'border-slate-700 opacity-50'
  }

  const getOptionIcon = (option) => {
    if (!isAnswered) return null

    if (option === question.correctAnswer) {
      return <Check className="w-5 h-5 text-green-500" />
    }

    if (option === selectedOption && option !== question.correctAnswer) {
      return <X className="w-5 h-5 text-red-500" />
    }

    return null
  }

  return (
    <div className="space-y-6">
      <div>
        <div className="text-sm text-blue-500 font-medium mb-2">Multiple Choice</div>
        <h3 className="text-xl font-semibold text-slate-100 mb-2">{question.question}</h3>
        {question.topic && (
          <div className="text-sm text-slate-400">
            Topic: {question.topic} {question.subtopic && `• ${question.subtopic}`}
          </div>
        )}
      </div>

      <div className="space-y-3">
        {question.options.map((option, index) => (
          <button
            key={index}
            onClick={() => handleOptionSelect(option)}
            disabled={isAnswered}
            className={`w-full p-4 rounded-lg border-2 text-left transition-all duration-200 ${getOptionStyle(option)} ${
              isAnswered ? 'cursor-default' : 'cursor-pointer'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-medium ${
                  selectedOption === option && !isAnswered
                    ? 'border-blue-500 bg-blue-500 text-white'
                    : 'border-slate-700 text-slate-400'
                }`}>
                  {String.fromCharCode(65 + index)}
                </div>
                <span className="text-slate-100">{option}</span>
              </div>
              {getOptionIcon(option)}
            </div>
          </button>
        ))}
      </div>

      {isAnswered && (
        <div className={`p-4 rounded-lg border ${
          selectedOption === question.correctAnswer
            ? 'border-green-500 bg-green-500/10'
            : 'border-red-500 bg-red-500/10'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {selectedOption === question.correctAnswer ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <X className="w-5 h-5 text-red-500" />
            )}
            <span className={`font-medium ${
              selectedOption === question.correctAnswer ? 'text-green-500' : 'text-red-500'
            }`}>
              {selectedOption === question.correctAnswer ? 'Correct!' : 'Incorrect'}
            </span>
          </div>
          {selectedOption !== question.correctAnswer && (
            <p className="text-slate-400 text-sm">
              The correct answer is: <span className="font-medium text-green-500">{question.correctAnswer}</span>
            </p>
          )}
        </div>
      )}
    </div>
  )
}

export default MultipleChoice

import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Target, Trophy } from 'lucide-react'
import TopicSelector from './components/TopicSelector'
import QuizContainer from './components/QuizContainer'
import { QuizProvider } from './hooks/useQuiz.jsx'

function App() {
  const [selectedTopic, setSelectedTopic] = useState(null)
  const [showQuiz, setShowQuiz] = useState(false)

  const handleStartQuiz = (topic) => {
    setSelectedTopic(topic)
    setShowQuiz(true)
  }

  const handleBackToTopics = () => {
    setShowQuiz(false)
    setSelectedTopic(null)
  }

  return (
    <QuizProvider>
      <div className="min-h-screen bg-slate-900">
        {/* Header */}
        <header className="bg-slate-800 border-b border-slate-700">
          <div className="max-w-6xl mx-auto px-4 py-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-100">History GCSE Revision</h1>
                <p className="text-slate-400">Master your knowledge with interactive quizzes</p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-6xl mx-auto px-4 py-8">
          {!showQuiz ? (
            <div className="space-y-8">
              {/* Welcome Section */}
              <div className="text-center space-y-4">
                <h2 className="text-3xl font-bold text-slate-100">Welcome to Your Revision Hub</h2>
                <p className="text-slate-400 max-w-2xl mx-auto">
                  Test your knowledge with multiple choice questions, true/false, matching exercises,
                  chronological ordering, and fill-in-the-gap challenges.
                </p>
              </div>

              {/* Features */}
              <div className="grid md:grid-cols-3 gap-6 mb-8">
                <div className="card text-center">
                  <Brain className="w-8 h-8 text-blue-500 mx-auto mb-3" />
                  <h3 className="font-semibold text-slate-100 mb-2">Multiple Question Types</h3>
                  <p className="text-slate-400 text-sm">MCQ, True/False, Matching, Ordering & Fill-in-the-gaps</p>
                </div>
                <div className="card text-center">
                  <Target className="w-8 h-8 text-green-500 mx-auto mb-3" />
                  <h3 className="font-semibold text-slate-100 mb-2">Focused Learning</h3>
                  <p className="text-slate-400 text-sm">Topic-based quizzes covering key GCSE content</p>
                </div>
                <div className="card text-center">
                  <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-3" />
                  <h3 className="font-semibold text-slate-100 mb-2">Track Progress</h3>
                  <p className="text-slate-400 text-sm">Monitor your performance and improvement</p>
                </div>
              </div>

              {/* Topic Selection */}
              <TopicSelector onStartQuiz={handleStartQuiz} />
            </div>
          ) : (
            <QuizContainer
              topic={selectedTopic}
              onBack={handleBackToTopics}
            />
          )}
        </main>
      </div>
    </QuizProvider>
  )
}

export default App

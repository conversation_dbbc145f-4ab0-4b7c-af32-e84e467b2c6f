import { useState } from 'react'
import { Check, X, Edit3 } from 'lucide-react'

const FillGaps = ({ question, onAnswer, isAnswered, userAnswer }) => {
  const [answer, setAnswer] = useState(userAnswer || '')
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered)

  const handleSubmit = () => {
    if (hasSubmitted || !answer.trim()) return

    const isCorrect = answer.trim().toLowerCase() === question.correctAnswer.toLowerCase()
    setHasSubmitted(true)
    onAnswer(answer.trim(), isCorrect)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !hasSubmitted && answer.trim()) {
      handleSubmit()
    }
  }

  const isCorrect = hasSubmitted && answer.trim().toLowerCase() === question.correctAnswer.toLowerCase()

  // Split the question to show the gap
  const questionParts = question.question.split('______')

  return (
    <div className="space-y-6">
      <div>
        <div className="text-sm text-pink-500 font-medium mb-2">Fill in the Gap</div>
        <h3 className="text-xl font-semibold text-slate-100 mb-2">Complete the sentence</h3>
        {question.topic && (
          <div className="text-sm text-slate-400">
            Topic: {question.topic} {question.subtopic && `• ${question.subtopic}`}
          </div>
        )}
      </div>

      <div className="card bg-slate-900 border-slate-700">
        <div className="text-lg text-slate-100 leading-relaxed">
          {questionParts.map((part, index) => (
            <span key={index}>
              {part}
              {index < questionParts.length - 1 && (
                <span className="inline-block">
                  {hasSubmitted ? (
                    <span className={`px-3 py-1 rounded font-medium ${
                      isCorrect 
                        ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                        : 'bg-red-500/20 text-red-400 border border-red-500/30'
                    }`}>
                      {answer || '______'}
                    </span>
                  ) : (
                    <input
                      type="text"
                      value={answer}
                      onChange={(e) => setAnswer(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your answer..."
                      className="input inline-block w-48 mx-1 text-center"
                      disabled={hasSubmitted}
                    />
                  )}
                </span>
              )}
            </span>
          ))}
        </div>
      </div>

      {!hasSubmitted && (
        <div className="flex items-center gap-4">
          <button
            onClick={handleSubmit}
            disabled={!answer.trim()}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <Edit3 className="w-4 h-4" />
            Submit Answer
          </button>
          <p className="text-slate-400 text-sm">
            Press Enter or click Submit when ready
          </p>
        </div>
      )}

      {hasSubmitted && (
        <div className={`p-4 rounded-lg border ${
          isCorrect
            ? 'border-green-500 bg-green-500/10'
            : 'border-red-500 bg-red-500/10'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {isCorrect ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <X className="w-5 h-5 text-red-500" />
            )}
            <span className={`font-medium ${
              isCorrect ? 'text-green-500' : 'text-red-500'
            }`}>
              {isCorrect ? 'Correct!' : 'Incorrect'}
            </span>
          </div>
          
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-slate-400">Your answer: </span>
              <span className={isCorrect ? 'text-green-400' : 'text-red-400'}>
                {answer || '(no answer)'}
              </span>
            </div>
            
            {!isCorrect && (
              <div>
                <span className="text-slate-400">Correct answer: </span>
                <span className="text-green-400 font-medium">
                  {question.correctAnswer}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="text-center">
        <p className="text-slate-400 text-sm">
          💡 Tip: Look for key terms, dates, names, or important concepts
        </p>
      </div>
    </div>
  )
}

export default FillGaps

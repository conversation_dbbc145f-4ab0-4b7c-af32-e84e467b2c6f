import { useState } from 'react'
import { Check, X } from 'lucide-react'

const TrueFalse = ({ question, onAnswer, isAnswered, userAnswer }) => {
  const [selectedAnswer, setSelectedAnswer] = useState(userAnswer !== undefined ? userAnswer : null)

  const handleAnswerSelect = (answer) => {
    if (isAnswered) return
    
    setSelectedAnswer(answer)
    const isCorrect = answer === question.correctAnswer
    onAnswer(answer, isCorrect)
  }

  const getButtonStyle = (answer) => {
    if (!isAnswered) {
      return selectedAnswer === answer
        ? 'border-blue-500 bg-blue-500/10'
        : 'border-slate-700 hover:border-blue-400 hover:bg-blue-400/5'
    }

    if (answer === question.correctAnswer) {
      return 'border-green-500 bg-green-500/10'
    }

    if (answer === selectedAnswer && answer !== question.correctAnswer) {
      return 'border-red-500 bg-red-500/10'
    }

    return 'border-slate-700 opacity-50'
  }

  const getButtonIcon = (answer) => {
    if (!isAnswered) return null

    if (answer === question.correctAnswer) {
      return <Check className="w-5 h-5 text-green-500" />
    }

    if (answer === selectedAnswer && answer !== question.correctAnswer) {
      return <X className="w-5 h-5 text-red-500" />
    }

    return null
  }

  return (
    <div className="space-y-6">
      <div>
        <div className="text-sm text-purple-500 font-medium mb-2">True or False</div>
        <h3 className="text-xl font-semibold text-slate-100 mb-2">{question.question}</h3>
        {question.topic && (
          <div className="text-sm text-slate-400">
            Topic: {question.topic} {question.subtopic && `• ${question.subtopic}`}
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={() => handleAnswerSelect(true)}
          disabled={isAnswered}
          className={`p-6 rounded-lg border-2 transition-all duration-200 ${getButtonStyle(true)} ${
            isAnswered ? 'cursor-default' : 'cursor-pointer'
          }`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${
                selectedAnswer === true && !isAnswered
                  ? 'border-blue-500 bg-blue-500 text-white'
                  : 'border-slate-700 text-slate-400'
              }`}>
                T
              </div>
              <span className="text-lg font-medium text-slate-100">True</span>
            </div>
            {getButtonIcon(true)}
          </div>
        </button>

        <button
          onClick={() => handleAnswerSelect(false)}
          disabled={isAnswered}
          className={`p-6 rounded-lg border-2 transition-all duration-200 ${getButtonStyle(false)} ${
            isAnswered ? 'cursor-default' : 'cursor-pointer'
          }`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${
                selectedAnswer === false && !isAnswered
                  ? 'border-blue-500 bg-blue-500 text-white'
                  : 'border-slate-700 text-slate-400'
              }`}>
                F
              </div>
              <span className="text-lg font-medium text-slate-100">False</span>
            </div>
            {getButtonIcon(false)}
          </div>
        </button>
      </div>

      {isAnswered && (
        <div className={`p-4 rounded-lg border ${
          selectedAnswer === question.correctAnswer
            ? 'border-green-500 bg-green-500/10'
            : 'border-red-500 bg-red-500/10'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {selectedAnswer === question.correctAnswer ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <X className="w-5 h-5 text-red-500" />
            )}
            <span className={`font-medium ${
              selectedAnswer === question.correctAnswer ? 'text-green-500' : 'text-red-500'
            }`}>
              {selectedAnswer === question.correctAnswer ? 'Correct!' : 'Incorrect'}
            </span>
          </div>
          <p className="text-slate-400 text-sm">
            The correct answer is: <span className="font-medium text-green-500">
              {question.correctAnswer ? 'True' : 'False'}
            </span>
          </p>
        </div>
      )}
    </div>
  )
}

export default TrueFalse
